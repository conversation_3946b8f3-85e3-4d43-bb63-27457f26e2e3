# Google Maps Grid Search - 数据驱动BFS架构 (v3.0)

## 📋 概述

这是一个完全重构的模块化版本，采用**数据驱动的广度优先搜索(BFS)架构**，从旧的递归深度优先搜索(DFS)架构演变而来。新架构提供了更好的可靠性、可恢复性、可扩展性和可观测性。

🎉 **项目已完成100%重构！** 新的数据驱动BFS架构已经完全取代了旧的递归DFS架构。

## 🏗️ 架构设计

### 核心架构原则

1. **数据驱动设计** - 以 `SearchOrchestration` 为核心状态对象
2. **广度优先搜索** - 非递归的分层处理引擎
3. **原子化状态管理** - 确保状态持久化的可靠性
4. **模块化设计** - 清晰的职责分离和低耦合

### 新架构层次结构

```
┌─────────────────────────────────────┐
│           Application Layer          │
│             (main.py)               │
├─────────────────────────────────────┤
│      Data Models & State            │
│    (data_models.py, state_manager)  │
├─────────────────────────────────────┤
│     Processing Engine Layer         │
│         (grid_engine.py)            │
├─────────────────────────────────────┤
│        Infrastructure Layer         │
│    (api/, visualization, report_)   │
└─────────────────────────────────────┘
```

## 📁 核心模块详解

### 🚀 `main.py` - 应用程序入口
- **职责**: 协调所有模块，执行主流程
- **关键功能**:
  - 命令行参数解析
  - 应用程序状态初始化
  - 状态管理器和网格引擎协调
  - 中断恢复支持

### 📊 `data_models.py` - 核心数据模型
- **职责**: 定义系统的核心数据结构
- **关键组件**:
  - `SearchStatus`: 搜索状态枚举
  - `GridCell`: 网格单元数据模型
  - `SearchOrchestration`: 搜索编排对象（单一事实来源）

### 💾 `state_manager.py` - 状态管理器
- **职责**: 原子化的状态持久化和恢复
- **关键功能**:
  - `save_state()`: 原子化保存状态
  - `load_state()`: 加载状态
  - `load_state_with_fallback()`: 带备份回退的加载

### ⚙️ `grid_engine.py` - 网格处理引擎
- **职责**: 实现广度优先的分层处理逻辑
- **关键功能**:
  - `process_next_layer()`: 处理下一层
  - `has_pending_work()`: 检查是否有待处理工作
  - 细化决策和子网格规划

### 🌐 `api_client.py` - API 客户端
- **职责**: 统一的API接口处理
- **功能**:
  - 真实Google Places API和模拟API切换
  - 错误处理和重试机制
  - 速率限制管理

### 🛠️ `app_state.py` - 应用程序状态
- **职责**: 模块化的应用程序状态管理
- **关键组件**:
  - `ApiState`: API状态管理
  - `SearchConfig`: 搜索配置
  - `SearchState`: 搜索状态跟踪
  - `VisualizationState`: 可视化状态

## 🔄 数据流

### 搜索流程

```mermaid
graph TD
    A[main.py] --> B[StateManager.load_state]
    B --> C[GridEngine.process_next_layer]
    C --> D[API调用和结果处理]
    D --> E{需要细化?}
    E -->|是| F[规划下一层]
    E -->|否| G[标记完成]
    F --> H[StateManager.save_state]
    G --> H
    H --> I{还有待处理工作?}
    I -->|是| C
    I -->|否| J[生成报告和可视化]
```

## 🚀 使用方法

### 基本使用

```bash
# 基本搜索
python -m src.main --location "Berlin, Germany" --place-type "restaurant"

# 试运行 (完全免费，无API调用)
python -m src.main --location "Berlin, Germany" --place-type "restaurant" --dry-run

# 限制API调用次数
python -m src.main --location "Berlin, Germany" --place-type "restaurant" --max-calls 100

# 生成可视化地图
python -m src.main --location "Berlin, Germany" --place-type "restaurant" --visualize
```

### 环境配置

创建 `.env` 文件：
```
GOOGLE_MAPS_API_KEY=your_api_key_here
```

### 中断恢复

新架构支持精确的中断恢复：
```bash
# 程序中断后，重新运行相同命令即可从断点恢复
python -m src.main --location "Berlin, Germany" --place-type "restaurant"
```

### 测试区域

内置测试区域用于开发和测试：
- **`alexanderplatz`**: 高密度区域
- **`tiergarten`**: 低密度区域  
- **`kreuzberg`**: 混合密度区域
- **`friedrichstrasse`**: 商业区域

```bash
# 在测试区域运行
python -m src.main --test-area alexanderplatz --dry-run --visualize
```

## 📊 输出文件

### 文件结构

```
output/
├── [work_dir]/
│   ├── orchestration.json          # 完整搜索状态（原子化，可序列化）
│   ├── found_place_ids.json        # 发现的地点ID
│   ├── summary.csv                 # CSV格式的汇总报告
│   ├── visualization_map.html      # 交互式可视化地图
│   ├── map_data.json               # 地图数据
│   └── detailed_place_data/
│       ├── place_*.json            # 详细地点信息
│       └── ...
└── ...
```

### 核心输出文件

- **`orchestration.json`**: 完整的搜索状态，支持原子化保存和恢复
- **`visualization_map.html`**: 交互式可视化地图
- **`summary.csv`**: CSV格式的汇总报告
- **`detailed_place_data/`**: 详细地点信息（JSON格式）

## 🧪 测试

项目拥有完整的测试覆盖：
- **API客户端测试**: 13/13 通过
- **数据模型测试**: 37/37 通过
- **状态管理器测试**: 20/20 通过
- **网格引擎测试**: 17/17 通过
- **集成测试**: 4/4 通过
- **总计**: 91/91 测试通过

```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行特定模块测试
python -m pytest tests/test_data_models.py -v
python -m pytest tests/test_grid_engine.py -v
```

## 🎯 新架构优势

### 1. 可靠性
- ✅ 原子化状态管理，防止数据损坏
- ✅ 中断后精确恢复能力
- ✅ 非递归设计，避免栈溢出

### 2. 可扩展性
- ✅ 广度优先架构为并行化奠定基础
- ✅ 模块化设计支持水平扩展
- ✅ 清晰的接口抽象

### 3. 可观测性
- ✅ 结构化的状态对象便于监控
- ✅ 详细的日志记录
- ✅ 实时统计信息

### 4. 可维护性
- ✅ 清晰的职责分离
- ✅ 完整的测试覆盖
- ✅ 统一的错误处理

## 🔧 配置参数

### 核心搜索参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--location` | "Berlin, Germany" | 搜索目标位置 |
| `--place-type` | "restaurant" | 地点类型 |
| `--dry-run` | false | 试运行模式（无API调用） |
| `--max-calls` | 4000 | 最大API调用数 |
| `--visualize` | false | 生成可视化地图 |
| `--test-area` | None | 使用预定义测试区域 |

### 算法参数 (algorithm_config.json)

```json
{
  "search": {
    "max_radius": 50000,
    "subdivision_threshold": 45,
    "max_refinement_levels": 4,
    "min_refinement_radius": 250,
    "initial_radius_factor": 0.25,
    "grid_step_factor": 0.8
  },
  "api": {
    "nearby_search_single_page_size": 20,
    "nearby_search_actual_limit": 60
  }
}
```

## 🔒 安全性

### API 安全
- **密钥管理**: 通过环境变量安全存储
- **速率限制**: 智能速率限制处理
- **错误处理**: 安全的错误信息处理

### 数据安全
- **原子化操作**: 状态保存采用原子化操作
- **备份机制**: 自动备份重要状态
- **输入验证**: 严格的参数验证

---
*版本 3.0.0 - 数据驱动BFS架构完成版*