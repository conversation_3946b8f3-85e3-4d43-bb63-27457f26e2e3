"""
简化网格处理引擎模块

重构后的网格引擎，大幅简化处理逻辑，保留所有高级功能。
"""

import time
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass

from src.data_models import SearchOrchestration, GridCell, SearchStatus
from src.api_client import APIClient
from src.state_manager import StateManager
from src.grid_algorithms import generate_mini_grid
from src.logging_config import get_logger
from src.exceptions import SearchError

logger = get_logger(__name__)


@dataclass
class ProcessingConfig:
    """简化的处理配置"""
    max_refinement_levels: int = 4
    initial_radius: float = 1000.0
    min_refinement_radius: float = 50.0
    max_api_calls_per_cell: int = 3
    page_delay_seconds: float = 2.0
    results_threshold_for_refinement: int = 20
    grid_overlap_factor: float = 0.8


@dataclass
class ProcessingStats:
    """处理统计信息"""
    total_cells: int = 0
    processed_cells: int = 0
    failed_cells: int = 0
    api_calls: int = 0
    places_found: int = 0
    refined_cells: int = 0


class GridEngine:
    """简化的网格处理引擎
    
    保留所有高级功能：
    - 断点续传
    - 递归细化
    - 实时可视化
    - 错误恢复
    """
    
    def __init__(
        self,
        orchestration: SearchOrchestration,
        api_client: APIClient,
        state_manager: StateManager,
        config: Optional[ProcessingConfig] = None,
        visualization_callback: Optional[Callable] = None,
        main_config: Optional[Any] = None,
    ):
        self.orchestration = orchestration
        self.api_client = api_client
        self.state_manager = state_manager
        self.config = config or ProcessingConfig()
        self.visualization_callback = visualization_callback
        self.main_config = main_config  # 用于API调用计数

        self.stats = ProcessingStats()
        self._update_stats()

        logger.info("网格处理引擎初始化完成")
    
    def _update_stats(self):
        """更新统计信息"""
        self.stats.total_cells = len(self.orchestration.cells)

        # 更准确的处理单元统计 - 包括所有已处理的状态
        self.stats.processed_cells = sum(
            1 for cell in self.orchestration.cells.values()
            if cell.status in [
                SearchStatus.SEARCH_COMPLETE,
                SearchStatus.REFINEMENT_COMPLETE,
                SearchStatus.REFINEMENT_NEEDED  # 也算作已处理
            ]
        )

        self.stats.failed_cells = sum(
            1 for cell in self.orchestration.cells.values()
            if cell.status == SearchStatus.FAILED
        )

        # 更新地点统计
        all_place_ids = set()
        for cell in self.orchestration.cells.values():
            all_place_ids.update(cell.place_ids)
        self.stats.places_found = len(all_place_ids)
    
    def has_pending_work(self) -> bool:
        """检查是否还有待处理的工作

        包括：
        1. 状态为 PENDING 的单元（需要搜索）
        2. 状态为 REFINEMENT_NEEDED 的单元（需要细化）
        """
        return any(
            cell.status in [SearchStatus.PENDING, SearchStatus.REFINEMENT_NEEDED]
            for cell in self.orchestration.cells.values()
        )
    
    def get_current_layer(self) -> int:
        """获取当前处理层级"""
        if not self.orchestration.cells:
            return 0
        return max(cell.layer_id for cell in self.orchestration.cells.values())
    
    def process_next_layer(self) -> Tuple[bool, ProcessingStats]:
        """处理下一层

        Returns:
            Tuple[bool, ProcessingStats]: (是否还有更多工作, 统计信息)
        """
        # 获取当前层的待处理单元
        current_layer = self.get_current_layer()
        pending_cells = [
            cell for cell in self.orchestration.cells.values()
            if cell.status == SearchStatus.PENDING and cell.layer_id == current_layer
        ]

        if not pending_cells:
            # 当前层没有待处理单元，检查是否需要生成下一层
            return self._plan_next_layer()

        # ===== 阶段开始日志 =====
        logger.info("=" * 60)
        logger.info(f"🚀 开始处理第 {current_layer} 层")
        logger.info(f"📊 第 {current_layer} 层有 {len(pending_cells)} 个待处理单元")
        logger.info("=" * 60)

        # 处理当前层的所有单元
        for i, cell in enumerate(pending_cells):
            try:
                # ===== 单元处理进度日志 =====
                progress = f"[{i + 1}/{len(pending_cells)}]"
                logger.info(f"🔄 {progress} 正在处理单元: {cell.cell_id}")
                logger.info(f"   📍 位置: ({cell.center_lat:.6f}, {cell.center_lng:.6f})")
                logger.info(f"   🎯 搜索半径: {cell.search_radius}m")

                self._process_single_cell(cell, i + 1, len(pending_cells))

                # 每10个单元保存一次状态
                if (i + 1) % 10 == 0:
                    self.state_manager.save_state(self.orchestration)
                    logger.info(f"💾 已处理 {i + 1} 个单元，状态已保存")

            except Exception as e:
                logger.error(f"❌ 处理单元 {cell.cell_id} 失败: {e}")
                cell.update_status(SearchStatus.FAILED)
                cell.error_message = str(e)

        # 保存最终状态
        self.state_manager.save_state(self.orchestration)

        # 更新统计信息
        self._update_stats()

        # 触发可视化回调
        if self.visualization_callback:
            try:
                self.visualization_callback("layer_complete", {
                    "layer": current_layer,
                    "orchestration": self.orchestration,
                    "stats": self.stats
                })
            except Exception as e:
                logger.debug(f"可视化回调失败: {e}")

        # ===== 阶段完成日志 =====
        logger.info("=" * 60)
        logger.info(f"✅ 第 {current_layer} 层处理完成")
        logger.info(f"📈 统计: 处理={self.stats.processed_cells}, 失败={self.stats.failed_cells}, 地点={self.stats.places_found}")
        logger.info("=" * 60)

        # 检查是否还有更多工作
        return self.has_pending_work(), self.stats
    
    def _process_single_cell(self, cell: GridCell, current_index: int = 0, total_count: int = 0, stats: Optional[ProcessingStats] = None):
        """处理单个网格单元

        Args:
            cell: 要处理的网格单元
            current_index: 当前处理的索引（用于进度显示）
            total_count: 总单元数（用于进度显示）
            stats: 统计信息对象
        """
        if stats is None:
            stats = self.stats

        # 更新状态为处理中
        cell.update_status(SearchStatus.PROCESSING)

        try:
            # 执行搜索
            results, api_calls_made = self._perform_search(cell)

            # 处理搜索结果
            self._handle_search_results(cell, results, api_calls_made, stats)

            # 详细的处理结果日志
            status_emoji = "🔄" if cell.status == SearchStatus.REFINEMENT_NEEDED else "✅"
            logger.info(f"   {status_emoji} 完成: 找到 {len(results)} 个结果, API调用 {api_calls_made} 次")
            if cell.status == SearchStatus.REFINEMENT_NEEDED:
                logger.info(f"   🔍 标记为需要细化 (结果数 >= {self.config.results_threshold_for_refinement})")

        except Exception as e:
            logger.error(f"❌ 单元 {cell.cell_id} 处理失败: {e}")
            cell.update_status(SearchStatus.FAILED)
            cell.error_message = str(e)
            stats.failed_cells += 1

    def _perform_search(self, cell: GridCell) -> tuple[List[Dict], int]:
        """执行搜索API调用

        Returns:
            tuple: (搜索结果列表, API调用次数)
        """
        all_results = []
        next_page_token = None
        api_calls_made = 0

        while api_calls_made < self.config.max_api_calls_per_cell:
            # 执行API调用
            response = self.api_client.perform_nearby_search(
                lat=cell.center_lat,
                lng=cell.center_lng,
                radius=cell.search_radius,
                place_type=self.orchestration.place_type,
                next_page_token=next_page_token,
                refine_level=cell.layer_id,
            )

            api_calls_made += 1
            self.stats.api_calls += 1

            # 增加主配置的API调用计数
            if self.main_config:
                self.main_config.increment_api_calls()

            # 处理响应
            if response.get("status") == "OK":
                results = response.get("results", [])
                all_results.extend(results)
                next_page_token = response.get("next_page_token")

                if not next_page_token:
                    break

                # 添加页面延迟（仅对真实API）
                if not getattr(self.api_client.config, 'dry_run', False):
                    time.sleep(self.config.page_delay_seconds)

            elif response.get("status") == "ZERO_RESULTS":
                break
            else:
                # API错误
                raise SearchError(f"API调用失败: {response.get('status')}")

        return all_results, api_calls_made

    def _handle_search_results(self, cell: GridCell, results: List[Dict], api_calls_made: int, stats: ProcessingStats):
        """处理搜索结果

        Args:
            cell: 网格单元
            results: 搜索结果列表
            api_calls_made: 实际进行的API调用次数
            stats: 统计信息对象
        """
        # 处理结果
        place_ids = {result.get("place_id") for result in results if result.get("place_id")}
        place_ids.discard(None)

        # 记录搜索结果 - 使用实际的API调用次数
        cell.record_search_results(len(results), place_ids, api_calls_made)

        # 更新统计信息
        stats.places_found += len(place_ids)

        # 同步到主配置对象
        if self.main_config:
            self.main_config.add_found_places(place_ids)

        # 使用改进的细化判断逻辑
        if self._should_refine_cell(cell, results):
            # 标记为需要细化
            cell.update_status(SearchStatus.REFINEMENT_NEEDED)
            stats.refined_cells += 1
            logger.debug(f"单元 {cell.cell_id} 需要细化 (结果数: {len(results)})")
        else:
            # 搜索完成
            cell.update_status(SearchStatus.SEARCH_COMPLETE)
            logger.debug(f"单元 {cell.cell_id} 搜索完成 (结果数: {len(results)})")

    def _plan_next_layer(self) -> Tuple[bool, ProcessingStats]:
        """规划下一层的细化"""
        # 找到需要细化的单元
        cells_to_refine = [
            cell for cell in self.orchestration.cells.values()
            if cell.status == SearchStatus.REFINEMENT_NEEDED
        ]

        if not cells_to_refine:
            # ===== 搜索完成日志 =====
            logger.info("=" * 60)
            logger.info("🎉 没有需要细化的单元，搜索完成")
            logger.info("=" * 60)
            return False, self.stats

        # ===== 细化阶段开始日志 =====
        next_layer = cells_to_refine[0].layer_id + 1
        logger.info("=" * 60)
        logger.info(f"🔍 开始生成第 {next_layer} 层细化网格")
        logger.info(f"📋 需要细化 {len(cells_to_refine)} 个单元")
        logger.info("=" * 60)

        # 为每个需要细化的单元生成子网格
        new_cells_count = 0
        for i, cell in enumerate(cells_to_refine):
            try:
                logger.info(f"🔄 [{i + 1}/{len(cells_to_refine)}] 细化单元: {cell.cell_id}")
                logger.info(f"   📊 原结果数: {cell.results_count}, 原半径: {cell.search_radius}m")

                # 生成子网格
                new_radius = cell.search_radius * self.config.grid_overlap_factor
                mini_grid_points = generate_mini_grid(
                    center_point=(cell.center_lat, cell.center_lng),
                    area_radius=cell.search_radius,
                    step_meters=new_radius,
                )

                logger.info(f"   🎯 新半径: {new_radius}m, 生成 {len(mini_grid_points)} 个子单元")

                # 创建新的网格单元
                for lat, lng in mini_grid_points:
                    # 让GridCell自动生成正确的ID（基于layer_id和坐标）
                    new_cell = GridCell(
                        cell_id="",  # 空字符串，__post_init__会自动生成正确的ID
                        status=SearchStatus.PENDING,
                        layer_id=cell.layer_id + 1,
                        center_lat=lat,
                        center_lng=lng,
                        search_radius=new_radius,
                        parent_id=cell.cell_id,
                    )
                    # 使用自动生成的ID作为键
                    self.orchestration.cells[new_cell.cell_id] = new_cell
                    new_cells_count += 1

                # 更新父单元状态
                cell.update_status(SearchStatus.REFINEMENT_COMPLETE)
                logger.info(f"   ✅ 完成细化，生成了 {len(mini_grid_points)} 个子单元")

            except Exception as e:
                logger.error(f"❌ 细化单元 {cell.cell_id} 失败: {e}")
                cell.update_status(SearchStatus.FAILED)
                cell.error_message = str(e)

        # ===== 细化完成日志 =====
        logger.info("=" * 60)
        logger.info(f"✅ 第 {next_layer} 层细化完成")
        logger.info(f"📈 总共生成了 {new_cells_count} 个新的细化单元")
        logger.info("=" * 60)

        # 保存状态
        self.state_manager.save_state(self.orchestration)

        # 触发可视化回调
        if self.visualization_callback:
            try:
                self.visualization_callback("refinement_complete", {
                    "refined_cells": len(cells_to_refine),
                    "new_cells": new_cells_count,
                    "orchestration": self.orchestration,
                    "stats": self.stats
                })
            except Exception as e:
                logger.debug(f"可视化回调失败: {e}")

        # 更新统计信息
        self._update_stats()

        return True, self.stats

    def _should_refine_cell(self, cell: GridCell, results: List[Dict]) -> bool:
        """改进的细化判断逻辑

        考虑多个因素来决定是否需要细化：
        1. 结果数量阈值
        2. 地点密度
        3. 重复性检查
        4. 层级和半径限制

        Args:
            cell: 要判断的网格单元
            results: 搜索结果列表

        Returns:
            bool: 是否需要细化
        """
        logger.debug(f"检查单元 {cell.cell_id} 是否需要细化: 结果数={len(results)}, 层级={cell.layer_id}, 半径={cell.search_radius}")

        # 基本条件检查
        if (cell.layer_id >= self.config.max_refinement_levels or
            cell.search_radius <= self.config.min_refinement_radius):
            logger.debug(f"单元 {cell.cell_id} 不满足基本条件: 层级={cell.layer_id}/{self.config.max_refinement_levels}, 半径={cell.search_radius}/{self.config.min_refinement_radius}")
            return False

        # 如果结果数量太少，不需要细化
        if len(results) < self.config.results_threshold_for_refinement:
            logger.debug(f"单元 {cell.cell_id} 结果数量不足: {len(results)} < {self.config.results_threshold_for_refinement}")
            return False

        # 计算地点密度（每平方公里的地点数）
        area_km2 = (3.14159 * (cell.search_radius / 1000) ** 2)  # 圆形面积，单位：平方公里
        density = len(results) / area_km2 if area_km2 > 0 else 0

        # 密度阈值：如果密度太低，可能不需要细化
        min_density_threshold = 10  # 每平方公里至少10个地点
        if density < min_density_threshold:
            logger.debug(f"单元 {cell.cell_id} 密度过低 ({density:.1f}/km²)，跳过细化")
            return False

        # 检查是否有重复的地点类型
        unique_types = set()
        for result in results:
            if 'types' in result:
                unique_types.update(result['types'])

        # 如果类型多样性很高，说明这是一个多样化的区域，值得细化
        type_diversity = len(unique_types)
        if type_diversity > 3:  # 如果有超过3种不同类型的地点
            logger.debug(f"单元 {cell.cell_id} 类型多样性高 ({type_diversity} 种类型)，需要细化")
            return True

        # 检查结果的地理分布
        has_good_distribution = self._has_good_geographic_distribution(results, cell)
        if has_good_distribution:
            # 如果地点分布均匀，可能不需要进一步细化
            logger.debug(f"单元 {cell.cell_id} 地点分布均匀，跳过细化")
            return False

        # 默认情况：如果结果数量超过阈值且密度合理，进行细化
        logger.debug(f"单元 {cell.cell_id} 满足细化条件 (结果数: {len(results)}, 密度: {density:.1f}/km²)")
        return True

    def _has_good_geographic_distribution(self, results: List[Dict], cell: GridCell) -> bool:
        """检查结果是否有良好的地理分布

        如果地点在搜索区域内分布相对均匀，可能不需要进一步细化

        Args:
            results: 搜索结果列表
            cell: 网格单元

        Returns:
            bool: 是否有良好的地理分布
        """
        if len(results) < 4:  # 结果太少，无法判断分布
            return False

        # 提取地点坐标
        locations = []
        for result in results:
            if 'geometry' in result and 'location' in result['geometry']:
                loc = result['geometry']['location']
                locations.append((loc['lat'], loc['lng']))

        if len(locations) < 4:
            return False

        # 计算地点之间的平均距离
        total_distance = 0
        count = 0
        for i in range(len(locations)):
            for j in range(i + 1, len(locations)):
                lat1, lng1 = locations[i]
                lat2, lng2 = locations[j]
                # 简单的距离计算（不需要精确的haversine）
                distance = ((lat1 - lat2) ** 2 + (lng1 - lng2) ** 2) ** 0.5
                total_distance += distance
                count += 1

        if count == 0:
            return False

        avg_distance = total_distance / count

        # 如果平均距离相对于搜索半径来说比较大，说明分布比较均匀
        # 将度数转换为大致的米数进行比较
        avg_distance_meters = avg_distance * 111000  # 大致转换：1度 ≈ 111km
        distribution_ratio = avg_distance_meters / cell.search_radius

        # 如果平均距离超过搜索半径的30%，认为分布较为均匀
        return distribution_ratio > 0.3

    def _should_refine_cell(self, cell: GridCell, results: List[Dict]) -> bool:
        """改进的细化判断逻辑

        考虑多个因素来决定是否需要细化：
        1. 结果数量阈值
        2. 地点密度
        3. 重复性检查
        4. 层级和半径限制

        Args:
            cell: 要判断的网格单元
            results: 搜索结果列表

        Returns:
            bool: 是否需要细化
        """
        # 基本条件检查
        if (cell.layer_id >= self.config.max_refinement_levels or
            cell.search_radius <= self.config.min_refinement_radius):
            return False

        # 如果结果数量太少，不需要细化
        if len(results) < self.config.results_threshold_for_refinement:
            return False

        # 计算地点密度（每平方公里的地点数）
        area_km2 = (3.14159 * (cell.search_radius / 1000) ** 2)  # 圆形面积，单位：平方公里
        density = len(results) / area_km2 if area_km2 > 0 else 0

        # 密度阈值：如果密度太低，可能不需要细化
        min_density_threshold = 10  # 每平方公里至少10个地点
        if density < min_density_threshold:
            logger.debug(f"单元 {cell.cell_id} 密度过低 ({density:.1f}/km²)，跳过细化")
            return False

        # 检查结果的地理分布
        if self._has_good_geographic_distribution(results, cell):
            # 如果地点分布均匀，可能不需要进一步细化
            logger.debug(f"单元 {cell.cell_id} 地点分布均匀，跳过细化")
            return False

        # 检查是否有重复的地点类型
        unique_types = set()
        for result in results:
            if 'types' in result:
                unique_types.update(result['types'])

        # 如果类型多样性很高，说明这是一个多样化的区域，值得细化
        type_diversity = len(unique_types)
        if type_diversity > 5:  # 如果有超过5种不同类型的地点
            logger.debug(f"单元 {cell.cell_id} 类型多样性高 ({type_diversity} 种类型)，需要细化")
            return True

        # 默认情况：如果结果数量超过阈值且密度合理，进行细化
        logger.debug(f"单元 {cell.cell_id} 满足细化条件 (结果数: {len(results)}, 密度: {density:.1f}/km²)")
        return True

    def _has_good_geographic_distribution(self, results: List[Dict], cell: GridCell) -> bool:
        """检查结果是否有良好的地理分布

        如果地点在搜索区域内分布相对均匀，可能不需要进一步细化

        Args:
            results: 搜索结果列表
            cell: 网格单元

        Returns:
            bool: 是否有良好的地理分布
        """
        if len(results) < 4:  # 结果太少，无法判断分布
            return False

        # 提取地点坐标
        locations = []
        for result in results:
            if 'geometry' in result and 'location' in result['geometry']:
                loc = result['geometry']['location']
                locations.append((loc['lat'], loc['lng']))

        if len(locations) < 4:
            return False

        # 计算地点之间的平均距离
        total_distance = 0
        count = 0
        for i in range(len(locations)):
            for j in range(i + 1, len(locations)):
                lat1, lng1 = locations[i]
                lat2, lng2 = locations[j]
                # 简单的距离计算（不需要精确的haversine）
                distance = ((lat1 - lat2) ** 2 + (lng1 - lng2) ** 2) ** 0.5
                total_distance += distance
                count += 1

        if count == 0:
            return False

        avg_distance = total_distance / count

        # 如果平均距离相对于搜索半径来说比较大，说明分布比较均匀
        # 将度数转换为大致的米数进行比较
        avg_distance_meters = avg_distance * 111000  # 大致转换：1度 ≈ 111km
        distribution_ratio = avg_distance_meters / cell.search_radius

        # 如果平均距离超过搜索半径的30%，认为分布较为均匀
        return distribution_ratio > 0.3
