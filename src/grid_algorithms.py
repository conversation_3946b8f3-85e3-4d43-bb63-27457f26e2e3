import math
from .logging_config import get_logger


# 获取日志记录器
logger = get_logger(__name__)


def haversine_distance(lat1, lng1, lat2, lng2):
    """计算两点之间的haversine距离（米）"""
    # 地球半径（米）

    R = 6371000
    # 将纬度和经度从度转换为弧度

    lat1, lng1, lat2, lng2 = map(math.radians, [lat1, lng1, lat2, lng2])
    # Haversine公式

    dlat = lat2 - lat1
    dlng = lng2 - lng1
    a = (
        math.sin(dlat / 2) ** 2
        + math.cos(lat1) * math.cos(lat2) * math.sin(dlng / 2) ** 2
    )
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    distance = R * c

    return distance


def meters_to_lat_degrees(meters):
    """将米转换为纬度度数（大约）"""

    return meters / 111320  # 1纬度度数大约为111.32公里


def meters_to_lng_degrees(meters, lat):
    """将米转换为指定纬度的经度度数"""
    # 在赤道上，1经度度数大约为111.32公里
    # 当我们远离赤道时，这会根据纬度的余弦值减小

    return meters / (111320 * math.cos(math.radians(lat)))


def calculate_max_distance_meters(bounds):
    """计算从中心到角落的最大距离（米）"""

    min_lat, min_lng, max_lat, max_lng = bounds
    center_lat = (min_lat + max_lat) / 2
    center_lng = (min_lng + max_lng) / 2
    # 计算到每个角落的距离

    corners = [
        (min_lat, min_lng),
        (min_lat, max_lng),
        (max_lat, min_lng),
        (max_lat, max_lng),
    ]

    max_distance = 0
    for corner_lat, corner_lng in corners:
        distance = haversine_distance(center_lat, center_lng, corner_lat, corner_lng)
        max_distance = max(max_distance, distance)

    return max_distance


# get_bounding_box has been moved to api/google_client.py (see TECHNICAL_DESIGN.md Task-C.1)
def generate_grid_points(bounds, step_meters):
    """生成覆盖边界框且间距均匀的网格点。"""

    min_lat, min_lng, max_lat, max_lng = bounds
    # 根据米步长计算度数步长
    # 对于纬度，转换大致是恒定的

    lat_step = meters_to_lat_degrees(step_meters)

    points = []
    current_lat = min_lat

    while current_lat <= max_lat:
        # For longitude, the conversion depends on the current latitude
        # 优化：只在纬度变化时重新计算经度步长
        lng_step = meters_to_lng_degrees(step_meters, current_lat)
        current_lng = min_lng

        while current_lng <= max_lng:
            # Store points with consistent precision
            points.append((round(current_lat, 6), round(current_lng, 6)))
            current_lng += lng_step

        current_lat += lat_step
    logger.info(f"生成了 {len(points)} 个网格点，步长大约为 {step_meters} 米。")

    return points


def generate_mini_grid(center_point, area_radius, step_meters):
    """在中心点周围的圆形区域内生成点网格。"""

    center_lat, center_lng = center_point
    # 计算度数步长

    lat_step = meters_to_lat_degrees(step_meters)
    lng_step = meters_to_lng_degrees(step_meters, center_lat)
    # 计算每个方向所需的步数
    # 将半径转换为度数偏移量（大约）

    lat_radius = meters_to_lat_degrees(area_radius)
    lng_radius = meters_to_lng_degrees(area_radius, center_lat)
    # 每个方向的步数（向上取整以确保覆盖）

    lat_steps = math.ceil(lat_radius / lat_step)
    lng_steps = math.ceil(lng_radius / lng_step)
    # 在一个包含圆的正方形中生成网格点

    points = []
    for i in range(-lat_steps, lat_steps + 1):
        for j in range(-lng_steps, lng_steps + 1):
            point_lat = center_lat + i * lat_step
            point_lng = center_lng + j * lng_step
            # 可选：仅当在圆形区域内时才添加
            # （这更准确但稍微复杂一些）

            if (
                haversine_distance(center_lat, center_lng, point_lat, point_lng)
                <= area_radius
            ):
                points.append((round(point_lat, 6), round(point_lng, 6)))

    return points


def calculate_search_parameters(
    bounds,
    max_radius,
    min_refinement_radius,
    max_refinement_levels,
    initial_radius_factor=0.25,
    grid_step_factor=0.8,
):
    """
    基于扫描范围动态计算搜索参数。

    Args:
        bounds (tuple): (min_lat, min_lng, max_lat, max_lng)
        max_radius (int): 最大允许半径
        min_refinement_radius (int): 最小细化半径
        max_refinement_levels (int): 最大细化层数
        initial_radius_factor (float): 初始半径因子（默认0.25）
        grid_step_factor (float): 网格步长因子（默认0.8）

    Returns:
        tuple: (initial_radius, initial_grid_step, refinement_radius_factors)
    """
    # 计算扫描范围的对角线距离作为参考大小
    diagonal_distance = haversine_distance(bounds[0], bounds[1], bounds[2], bounds[3])

    # 1. 计算 INITIAL_RADIUS
    # 初始半径设为对角线距离的因子倍，确保初始搜索圈能覆盖大部分区域，但不超过最大限制
    initial_radius = min(diagonal_distance * initial_radius_factor, max_radius)

    # 确保初始半径至少比最小细化半径大一个数量级，避免无效计算
    # 同时确保不超过最大半径限制
    if initial_radius < min_refinement_radius * 10:
        initial_radius = min(min_refinement_radius * 10, max_radius)

    # 2. 计算 INITIAL_GRID_STEP
    # 网格步长设为初始半径的因子倍，以确保网格点之间的搜索区域有足够的重叠
    initial_grid_step = initial_radius * grid_step_factor

    # 3. 计算 REFINEMENT_RADIUS_FACTORS
    # 使用等比缩放（几何级数）来计算细化因子，确保平滑收敛
    if initial_radius > min_refinement_radius and max_refinement_levels > 0:
        # 计算总的缩减比例
        total_reduction = initial_radius / min_refinement_radius

        # 计算每层缩放的公比
        # 例如，如果总缩减是100，有4层，那么每层缩放 100^(1/4)
        common_ratio = total_reduction ** (1 / max_refinement_levels)

        refinement_radius_factors = [common_ratio] * max_refinement_levels
    else:
        refinement_radius_factors = [1.0] * max_refinement_levels

    logger.info(
        f"动态计算参数: 初始半径={initial_radius:.2f}m, 初始步长={initial_grid_step:.2f}m, 细化因子={[round(f, 2) for f in refinement_radius_factors]}"
    )

    return initial_radius, initial_grid_step, refinement_radius_factors
