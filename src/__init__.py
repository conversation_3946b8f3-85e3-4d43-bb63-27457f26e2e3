"""
Google Maps Grid Search - Modular Architecture

A Python-based tool for extracting place data from Google Maps using the Google Places API
with adaptive grid-based searching and modular architecture.
"""

# from .main import main (removed to prevent import chain during testing)
from .config import create_parser
from .api_client import APIClient
from .grid_algorithms import generate_grid_points, calculate_search_parameters
from .api.google_client import GooglePlacesAPI
from .visualization import save_map_data, generate_visualization_from_orchestration

# Removed: report_generator (no longer needed after refactoring)
from .logging_config import get_logger, configure_logging, LoggerFactory

__version__ = "2.0.0"
__author__ = "Google Maps Grid Search Team"

__all__ = [
    "create_parser",
    "APIClient",
    "generate_grid_points",
    "calculate_search_parameters",
    "GooglePlacesAPI",
    "generate_visualization_from_orchestration",
    "save_map_data",
    "get_logger",
    "configure_logging",
    "LoggerFactory",
]
