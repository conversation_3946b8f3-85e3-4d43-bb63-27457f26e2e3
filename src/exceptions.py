"""
简化异常处理模块

重构后的异常系统，只保留2个核心异常类。
提供更直接的错误信息，减少异常包装层次。
"""


class SearchError(Exception):
    """搜索相关错误
    
    所有搜索过程中的错误都使用此异常类。
    包括API调用失败、网络错误、数据处理错误等。
    """
    
    def __init__(self, message: str, details: dict = None):
        super().__init__(message)
        self.message = message
        self.details = details or {}
    
    def __str__(self):
        return f"搜索错误: {self.message}"


class ConfigError(Exception):
    """配置相关错误
    
    配置验证失败、参数错误等配置相关问题使用此异常类。
    """
    
    def __init__(self, message: str, config_field: str = None):
        super().__init__(message)
        self.message = message
        self.config_field = config_field
    
    def __str__(self):
        if self.config_field:
            return f"配置错误 ({self.config_field}): {self.message}"
        return f"配置错误: {self.message}"


# 为了向后兼容，保留一些常用的异常别名
GridRuntimeError = SearchError
APIError = SearchError
NetworkError = SearchError
DataError = SearchError
APIRateLimitError = SearchError
