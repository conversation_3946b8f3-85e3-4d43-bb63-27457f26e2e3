"""
可视化模块

为 Google Maps Grid Search 项目提供可视化功能，包括地图生成功能。

基于 TECHNICAL_DESIGN.md 中的设计规范。
"""

import json
import os
from datetime import datetime
from typing import List, Tuple, Dict, Any, Optional
import logging

from src.data_models import SearchStatus

logger = logging.getLogger(__name__)

# Try to import folium

try:
    import folium

    FOLIUM_AVAILABLE = True
except ImportError:
    FOLIUM_AVAILABLE = False
    logger.warning("Folium 库不可用，将跳过地图生成")


def generate_visualization_from_orchestration(
    orchestration,
    output_file: str,
    search_bounds: Optional[Tuple[float, float, float, float]] = None,
    is_final: bool = True,
) -> bool:
    """基于搜索编排对象生成可视化地图。

    Args:
        orchestration: 搜索编排对象
        output_file: 输出文件路径
        search_bounds: 搜索边界 (min_lat, min_lng, max_lat, max_lng)
        is_final: 是否为最终可视化

    Returns:
        bool: 生成是否成功

    Raises:
        RuntimeError: 可视化生成失败时抛出
    """
    if not FOLIUM_AVAILABLE:
        logger.warning("Folium 库不可用，跳过可视化生成")
        return False

    try:
        logger.info("开始生成可视化地图...")

        # 提取数据
        all_grid_points = []
        all_refinement_points = []

        for cell in orchestration.cells.values():
            # 添加网格点
            all_grid_points.append((cell.center_lat, cell.center_lng))

            # 添加需要细化的点
            if (
                cell.needs_refinement()
                or cell.status == "refinement_needed"
                or cell.status == "refinement_complete"
            ):
                all_refinement_points.append((cell.center_lat, cell.center_lng))

        if not all_grid_points:
            logger.warning("没有网格点数据，跳过可视化生成")
            return False

        # 计算地图中心和边界
        if search_bounds:
            # 使用搜索边界
            center_lat = (search_bounds[0] + search_bounds[2]) / 2
            center_lng = (search_bounds[1] + search_bounds[3]) / 2
        else:
            # 从网格点计算中心
            center_lat = sum(point[0] for point in all_grid_points) / len(
                all_grid_points
            )
            center_lng = sum(point[1] for point in all_grid_points) / len(
                all_grid_points
            )

        # 创建地图
        map_obj = folium.Map(
            location=[center_lat, center_lng], zoom_start=12, tiles="OpenStreetMap"
        )

        # 添加搜索边界（如果有）
        if search_bounds:
            min_lat, min_lng, max_lat, max_lng = search_bounds
            folium.Rectangle(
                bounds=[[min_lat, min_lng], [max_lat, max_lng]],
                color="blue",
                fill=False,
                weight=2,
                popup="搜索区域边界",
            ).add_to(map_obj)

        # 添加覆盖范围圆圈（在底层）
        _add_coverage_circles_to_map(map_obj, orchestration)

        # 添加网格点
        if all_grid_points:
            for lat, lng in all_grid_points:
                folium.CircleMarker(
                    location=[lat, lng],
                    radius=3,
                    color="blue",
                    fill=True,
                    fillColor="blue",
                    fillOpacity=0.6,
                    popup=f"网格点 ({lat:.4f}, {lng:.4f})",
                ).add_to(map_obj)

        # 添加细化点
        if all_refinement_points:
            for lat, lng in all_refinement_points:
                folium.CircleMarker(
                    location=[lat, lng],
                    radius=5,
                    color="red",
                    fill=True,
                    fillColor="red",
                    fillOpacity=0.8,
                    popup=f"细化点 ({lat:.4f}, {lng:.4f})",
                ).add_to(map_obj)

        # 添加统计信息
        stats_text = f"""
        搜索统计信息:
        - 总网格点数: {len(all_grid_points)}
        - 细化点数: {len(all_refinement_points)}
        - 发现地点数: {len(orchestration.get_all_place_ids())}
        - 总API调用: {orchestration.metrics.get('total_api_calls', 0)}
        - 处理单元数: {len(orchestration.cells)}
        - 完成层级: {len(orchestration.completed_layers)}
        """

        if is_final:
            stats_text += "\n- 状态: 最终结果"
        else:
            stats_text += "\n- 状态: 进行中"

        folium.Marker(
            location=[center_lat, center_lng],
            popup=stats_text,
            icon=folium.Icon(color="green", icon="info-sign"),
        ).add_to(map_obj)

        # 添加图例
        legend_html = """
        <div style="position: fixed; 
                    top: 10px; right: 10px; width: 150px; height: 90px; 
                    background-color:white; border:2px solid grey; z-index:9999; 
                    font-size:14px; padding: 10px
                    ">
        <b>图例</b><br>
        <i class="fa fa-circle" style="color:blue"></i> 网格点<br>
        <i class="fa fa-circle" style="color:red"></i> 细化点
        </div>
        """
        map_obj.get_root().html.add_child(folium.Element(legend_html))

        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # 保存地图
        map_obj.save(output_file)
        logger.info(f"可视化地图已保存到: {output_file}")

        return True

    except Exception as e:
        error_msg = f"生成可视化地图失败: {e}"
        logger.error(f"*** {error_msg} ***")
        raise RuntimeError(error_msg) from e


def _validate_visualization_inputs(orchestration, output_file: str) -> bool:
    """验证可视化输入参数

    Args:
        orchestration: 搜索编排对象
        output_file: 输出文件路径

    Returns:
        bool: 验证是否通过
    """
    if not FOLIUM_AVAILABLE:
        logger.warning("Folium 库不可用，跳过中间可视化生成")
        return False

    if orchestration is None:
        logger.warning("编排对象为空，跳过中间可视化生成")
        return False

    if not orchestration.cells:
        logger.warning("没有网格单元数据，跳过中间可视化生成")
        return False

    return True


def _extract_cell_points_by_status(orchestration):
    """按状态提取网格点

    Args:
        orchestration: 搜索编排对象

    Returns:
        tuple: (pending_points, processing_points, completed_points, failed_points, refinement_points)
    """
    pending_points = []
    processing_points = []
    completed_points = []
    failed_points = []
    refinement_points = []

    for cell in orchestration.cells.values():
        lat, lng = cell.center_lat, cell.center_lng
        status = (
            cell.status.value if hasattr(cell.status, "value") else str(cell.status)
        )

        if status == "pending":
            pending_points.append((lat, lng))
        elif status == "processing":
            processing_points.append((lat, lng))
        elif status in ["search_complete", "refinement_complete"]:
            completed_points.append((lat, lng))
        elif status == "failed":
            failed_points.append((lat, lng))
        elif status in ["refinement_needed"]:
            refinement_points.append((lat, lng))

    return pending_points, processing_points, completed_points, failed_points, refinement_points


def _calculate_map_center(orchestration) -> Tuple[float, float]:
    """计算地图中心点

    Args:
        orchestration: 搜索编排对象

    Returns:
        tuple: (center_lat, center_lng)
    """
    all_points = []
    for cell in orchestration.cells.values():
        all_points.append((cell.center_lat, cell.center_lng))

    if all_points:
        center_lat = sum(point[0] for point in all_points) / len(all_points)
        center_lng = sum(point[1] for point in all_points) / len(all_points)
    else:
        center_lat, center_lng = 52.5200, 13.4050

    return center_lat, center_lng


def generate_intermediate_visualization(
    orchestration,
    output_file: str,
    last_updated_cell: Optional[str] = None,
    search_bounds: Optional[Tuple[float, float, float, float]] = None,
) -> bool:
    """生成中间过程的实时可视化更新。

    Args:
        orchestration: 搜索编排对象
        output_file: 输出文件路径
        last_updated_cell: 最近更新的网格单元ID
        search_bounds: 搜索边界

    Returns:
        bool: 生成是否成功
    """
    # 验证输入参数
    if not _validate_visualization_inputs(orchestration, output_file):
        return False

    try:
        logger.info(
            f"开始生成中间可视化地图更新... 当前状态: {orchestration.get_overall_status()}"
        )

        # 提取数据并分类状态
        pending_points = []
        processing_points = []
        completed_points = []
        failed_points = []
        refinement_points = []

        for cell in orchestration.cells.values():
            lat, lng = cell.center_lat, cell.center_lng
            status = (
                cell.status.value if hasattr(cell.status, "value") else str(cell.status)
            )

            if status == "pending":
                pending_points.append((lat, lng))
            elif status == "processing":
                processing_points.append((lat, lng))
            elif status in ["search_complete", "refinement_complete"]:
                completed_points.append((lat, lng))
            elif status == "failed":
                failed_points.append((lat, lng))
            elif status in ["refinement_needed"]:
                refinement_points.append((lat, lng))

        # 计算地图中心
        all_points = []
        for cell in orchestration.cells.values():
            all_points.append((cell.center_lat, cell.center_lng))

        if all_points:
            center_lat = sum(point[0] for point in all_points) / len(all_points)
            center_lng = sum(point[1] for point in all_points) / len(all_points)
        else:
            center_lat, center_lng = 52.5200, 13.4050

        # 创建地图
        map_obj = folium.Map(
            location=[center_lat, center_lng], zoom_start=13, tiles="OpenStreetMap"
        )

        # 添加搜索边界
        if search_bounds:
            min_lat, min_lng, max_lat, max_lng = search_bounds
            folium.Rectangle(
                bounds=[[min_lat, min_lng], [max_lat, max_lng]],
                color="black",
                fill=False,
                weight=2,
                popup="搜索区域边界",
            ).add_to(map_obj)

        # 添加覆盖范围圆圈（在底层）
        _add_coverage_circles_to_map(map_obj, orchestration)

        # 添加不同状态的标记
        marker_count = 0

        # 待处理网格点 - 灰色
        for lat, lng in pending_points:
            folium.CircleMarker(
                location=[lat, lng],
                radius=4,
                color="gray",
                fill=True,
                fillColor="gray",
                fillOpacity=0.6,
                popup=f"待处理网格 ({lat:.4f}, {lng:.4f})",
            ).add_to(map_obj)
            marker_count += 1

        # 处理中网格点 - 黄色
        for lat, lng in processing_points:
            folium.CircleMarker(
                location=[lat, lng],
                radius=4,
                color="orange",
                fill=True,
                fillColor="yellow",
                fillOpacity=0.8,
                popup=f"处理中网格 ({lat:.4f}, {lng:.4f})",
            ).add_to(map_obj)
            marker_count += 1

        # 已完成网格点 - 绿色
        for lat, lng in completed_points:
            folium.CircleMarker(
                location=[lat, lng],
                radius=4,
                color="green",
                fill=True,
                fillColor="green",
                fillOpacity=0.8,
                popup=f"已完成网格 ({lat:.4f}, {lng:.4f})",
            ).add_to(map_obj)
            marker_count += 1

        # 失败网格点 - 红色
        for lat, lng in failed_points:
            folium.CircleMarker(
                location=[lat, lng],
                radius=4,
                color="red",
                fill=True,
                fillColor="red",
                fillOpacity=0.8,
                popup=f"失败网格 ({lat:.4f}, {lng:.4f})",
            ).add_to(map_obj)
            marker_count += 1

        # 需细化网格点 - 紫色
        for lat, lng in refinement_points:
            folium.CircleMarker(
                location=[lat, lng],
                radius=6,
                color="purple",
                fill=True,
                fillColor="purple",
                fillOpacity=0.8,
                popup=f"需细化网格 ({lat:.4f}, {lng:.4f})",
            ).add_to(map_obj)
            marker_count += 1

        # 高亮最近更新的单元格
        if last_updated_cell and last_updated_cell in orchestration.cells:
            cell = orchestration.cells[last_updated_cell]
            lat, lng = cell.center_lat, cell.center_lng
            folium.CircleMarker(
                location=[lat, lng],
                radius=8,
                color="gold",
                fill=True,
                fillColor="gold",
                fillOpacity=0.9,
                weight=3,
                popup=f"最近更新 ({lat:.4f}, {lng:.4f}) - 状态: {cell.status.value}",
            ).add_to(map_obj)

        # 添加实时统计信息
        processing_progress = f"""
        实时搜索进度:
        - 总网格单元: {len(orchestration.cells)}
        - 待处理: {len(pending_points)}
        - 处理中: {len(processing_points)}
        - 已完成: {len(completed_points)}
        - 需细化: {len(refinement_points)}
        - 失败: {len(failed_points)}
        - 发现地点: {len(orchestration.get_all_place_ids())}
        - 当前层级: {orchestration.current_layer}
        - 完成层级: {len(orchestration.completed_layers)}
        - API调用: {orchestration.metrics.get('total_api_calls', 0)}
        """

        if last_updated_cell:
            processing_progress += f"\n- 最后更新: {last_updated_cell}"

        status = orchestration.get_overall_status()
        processing_progress += f"\n- 总体状态: {status}"

        folium.Marker(
            location=[center_lat, center_lng],
            popup=processing_progress,
            icon=folium.Icon(color="blue", icon="info-sign"),
        ).add_to(map_obj)

        # 添加颜色图例
        legend_html = f"""
        <div style="position: fixed; 
                    top: 10px; right: 10px; width: 180px; min-height: 150px; 
                    background-color:white; border:2px solid grey; z-index:9999; 
                    font-size:12px; padding: 10px
                    ">
        <b>实时图例</b><br>
        <i style="color:gray">●</i> 待处理 ({len(pending_points)})<br>
        <i style="color:orange">●</i> 处理中 ({len(processing_points)})<br>
        <i style="color:green">●</i> 已完成 ({len(completed_points)})<br>
        <i style="color:red">●</i> 失败 ({len(failed_points)})<br>
        <i style="color:purple">●</i> 需细化 ({len(refinement_points)})<br>
        <i style="color:gold">●</i> 最近更新
        </div>
        """
        map_obj.get_root().html.add_child(folium.Element(legend_html))

        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # 保存地图
        map_obj.save(output_file)
        logger.info(
            f"中间可视化地图已保存到: {output_file} (包含 {marker_count} 个标记)"
        )

        return True

    except Exception as e:
        error_msg = f"生成中间可视化地图失败: {e}"
        logger.error(f"*** {error_msg} ***")
        raise RuntimeError(error_msg) from e


# 其他函数保持不变...
def save_map_data(
    grid_points: List[Tuple[float, float]],
    refinement_points: List[Tuple[float, float]],
    place_coordinates: List[Tuple[float, float]],
    output_file: str,
) -> bool:
    """保存地图数据到JSON文件。"""
    try:
        logger.info("保存地图数据...")

        map_data = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "grid_points_count": len(grid_points),
                "refinement_points_count": len(refinement_points),
                "place_coordinates_count": len(place_coordinates),
            },
            "grid_points": [{"lat": lat, "lng": lng} for lat, lng in grid_points],
            "refinement_points": [
                {"lat": lat, "lng": lng} for lat, lng in refinement_points
            ],
            "place_coordinates": [
                {"lat": lat, "lng": lng} for lat, lng in place_coordinates
            ],
        }

        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # 保存数据
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(map_data, f, indent=2, ensure_ascii=False)

        logger.info(f"地图数据已保存到: {output_file}")
        return True

    except Exception as e:
        error_msg = f"保存地图数据失败: {e}"
        logger.error(f"*** {error_msg} ***")
        raise RuntimeError(error_msg) from e


# 其他保持不变的函数...
def load_map_data(input_file: str) -> Optional[Dict[str, Any]]:
    """从JSON文件加载地图数据。"""
    try:
        if not os.path.exists(input_file):
            logger.warning(f"地图数据文件不存在: {input_file}")
            return None

        logger.info(f"加载地图数据: {input_file}")

        with open(input_file, "r", encoding="utf-8") as f:
            map_data = json.load(f)

        logger.info(
            f"成功加载地图数据，包含 {map_data['metadata']['grid_points_count']} 个网格点"
        )
        return map_data

    except json.JSONDecodeError as e:
        error_msg = f"地图数据文件格式错误: {e}"
        logger.error(f"*** {error_msg} ***")
        raise RuntimeError(error_msg) from e

    except Exception as e:
        error_msg = f"加载地图数据失败: {e}"
        logger.error(f"*** {error_msg} ***")
        raise RuntimeError(error_msg) from e


def combine_multiple_maps(map_files: List[str], output_file: str) -> bool:
    """合并多个地图数据文件。"""
    try:
        logger.info(f"合并 {len(map_files)} 个地图数据文件...")

        combined_data = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "combined_from": map_files,
                "total_files": len(map_files),
            },
            "grid_points": [],
            "refinement_points": [],
            "place_coordinates": [],
        }

        # 合并所有地图数据
        for map_file in map_files:
            if not os.path.exists(map_file):
                logger.warning(f"地图数据文件不存在，跳过: {map_file}")
                continue

            map_data = load_map_data(map_file)
            if map_data:
                combined_data["grid_points"].extend(map_data.get("grid_points", []))
                combined_data["refinement_points"].extend(
                    map_data.get("refinement_points", [])
                )
                combined_data["place_coordinates"].extend(
                    map_data.get("place_coordinates", [])
                )

        # 去重
        combined_data["grid_points"] = _remove_duplicate_points(
            combined_data["grid_points"]
        )
        combined_data["refinement_points"] = _remove_duplicate_points(
            combined_data["refinement_points"]
        )
        combined_data["place_coordinates"] = _remove_duplicate_points(
            combined_data["place_coordinates"]
        )

        # 更新元数据
        combined_data["metadata"]["grid_points_count"] = len(
            combined_data["grid_points"]
        )
        combined_data["metadata"]["refinement_points_count"] = len(
            combined_data["refinement_points"]
        )
        combined_data["metadata"]["place_coordinates_count"] = len(
            combined_data["place_coordinates"]
        )

        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # 保存合并后的数据
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(combined_data, f, indent=2, ensure_ascii=False)

        logger.info(f"合并后的地图数据已保存到: {output_file}")
        logger.info(
            f"合并后统计: {len(combined_data['grid_points'])} 网格点, {len(combined_data['refinement_points'])} 细化点"
        )

        return True

    except Exception as e:
        error_msg = f"合并地图数据失败: {e}"
        logger.error(f"*** {error_msg} ***")
        raise RuntimeError(error_msg) from e


def _remove_duplicate_points(points: List[Dict[str, float]]) -> List[Dict[str, float]]:
    """移除重复的点。"""
    seen = set()
    unique_points = []

    for point in points:
        key = (point["lat"], point["lng"])
        if key not in seen:
            seen.add(key)
            unique_points.append(point)

    return unique_points


def reset_visualization_data(visualization_state) -> None:
    """重置可视化状态。"""
    try:
        visualization_state.clear()
        logger.debug("可视化状态已重置")
    except Exception as e:
        logger.error(f"重置可视化状态失败: {e}")
        raise RuntimeError(f"重置可视化状态失败: {e}") from e


def get_visualization_data(visualization_state) -> List[Tuple[float, float]]:
    """获取可视化数据。"""
    try:
        return visualization_state.place_coords_for_viz
    except Exception as e:
        logger.error(f"获取可视化数据失败: {e}")
        raise RuntimeError(f"获取可视化数据失败: {e}") from e


def is_visualization_available() -> bool:
    """检查可视化功能是否可用。"""
    return FOLIUM_AVAILABLE


def get_visualization_status() -> Dict[str, Any]:
    """获取可视化功能状态。"""
    return {
        "available": FOLIUM_AVAILABLE,
        "library": "folium",
        "version": folium.__version__ if FOLIUM_AVAILABLE else None,
        "capabilities": (
            [
                "interactive_map_generation",
                "heatmap_generation",
                "data_export",
                "map_combination",
            ]
            if FOLIUM_AVAILABLE
            else []
        ),
    }


# Removed deprecated visualize_search_results function


def _add_coverage_circles_to_map(map_obj, orchestration):
    """添加搜索覆盖范围圆圈到地图"""
    if not orchestration or not orchestration.cells:
        return

    # 按层级排序，确保父级圆圈在底层，子级圆圈在上层
    cells_by_layer = {}
    for cell in orchestration.cells.values():
        layer = cell.layer_id
        if layer not in cells_by_layer:
            cells_by_layer[layer] = []
        cells_by_layer[layer].append(cell)

    # 从最低层级开始绘制（父级在底层，子级在上层）
    for layer in sorted(cells_by_layer.keys()):
        for cell in cells_by_layer[layer]:
            _add_single_coverage_circle(map_obj, cell)


def _add_single_coverage_circle(map_obj, cell):
    """添加单个cell的覆盖范围圆圈"""
    # 确定圆圈样式
    has_refinement = (
        cell.status in [SearchStatus.REFINEMENT_NEEDED, SearchStatus.REFINEMENT_COMPLETE] or
        len(cell.children_ids) > 0
    )

    # 根据是否有细化选择样式
    if has_refinement:
        # 有细化：实线圆圈
        dash_array = None
        opacity = 0.7
        weight = 3
    else:
        # 无细化：虚线圆圈
        dash_array = "15,10"
        opacity = 0.6
        weight = 2

    # 根据状态选择颜色
    if cell.status == SearchStatus.PENDING:
        color = "gray"
    elif cell.status == SearchStatus.PROCESSING:
        color = "orange"
    elif cell.status == SearchStatus.SEARCH_COMPLETE:
        color = "green"
    elif cell.status == SearchStatus.REFINEMENT_NEEDED:
        color = "purple"
    elif cell.status == SearchStatus.REFINEMENT_COMPLETE:
        color = "blue"
    elif cell.status == SearchStatus.FAILED:
        color = "red"
    else:
        color = "gray"

    # 添加覆盖范围圆圈
    folium.Circle(
        location=[cell.center_lat, cell.center_lng],
        radius=cell.search_radius,
        color=color,
        weight=weight,
        opacity=opacity,
        fill=False,
        dashArray=dash_array,
        popup=f"覆盖范围 - 半径: {cell.search_radius}m<br>"
              f"状态: {cell.status.value}<br>"
              f"层级: {cell.layer_id}<br>"
              f"结果数: {cell.results_count}<br>"
              f"{'已细化' if has_refinement else '未细化'}",
    ).add_to(map_obj)
