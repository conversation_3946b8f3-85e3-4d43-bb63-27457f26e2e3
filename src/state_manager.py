"""
状态管理器模块

为 Google Maps Grid Search 项目提供原子化的状态持久化管理。
实现 SearchOrchestration 对象的原子化加载和保存功能。

基于 TECHNICAL_DESIGN.md 中的设计规范。
"""

import json
import shutil
from typing import Optional
from pathlib import Path

from src.data_models import SearchOrchestration
from src.logging_config import get_logger


# 获取日志记录器
logger = get_logger(__name__)


class StateManager:
    """状态管理器

    负责 SearchOrchestration 对象的原子化持久化和加载。
    通过"写入临时文件后重命名"的策略确保 save_state 操作的原子性。

    属性:
        work_dir: 工作目录路径
        state_file: 状态文件名
        backup_enabled: 是否启用备份功能
    """

    def __init__(
        self,
        work_dir: str = ".",
        state_file: str = "orchestration.json",
        backup_enabled: bool = True,
    ):
        """初始化状态管理器

        Args:
            work_dir: 工作目录路径
            state_file: 状态文件名
            backup_enabled: 是否启用备份功能
        """
        self.work_dir = Path(work_dir)
        self.state_file = state_file
        self.backup_enabled = backup_enabled
        self._ensure_work_dir_exists()

    def _ensure_work_dir_exists(self) -> None:
        """确保工作目录存在"""
        self.work_dir.mkdir(parents=True, exist_ok=True)

    def get_state_file_path(self) -> Path:
        """获取状态文件完整路径"""
        return self.work_dir / self.state_file

    def get_backup_file_path(self) -> Path:
        """获取备份文件完整路径"""
        return self.work_dir / f"{self.state_file}.backup"

    def get_temp_file_path(self) -> Path:
        """获取临时文件完整路径"""
        return self.work_dir / f"{self.state_file}.tmp"

    def save_state(self, orchestration: SearchOrchestration) -> bool:
        """原子化保存状态

        通过"写入临时文件后重命名"的策略确保操作的原子性。
        如果启用备份功能，还会创建当前状态的备份。

        Args:
            orchestration: 要保存的搜索编排对象

        Returns:
            bool: 保存是否成功

        Raises:
            IOError: 文件操作失败
            TypeError: 序列化失败
        """
        try:
            state_file_path = self.get_state_file_path()
            temp_file_path = self.get_temp_file_path()

            # 如果启用了备份且状态文件存在，则创建备份
            if self.backup_enabled and state_file_path.exists():
                backup_file_path = self.get_backup_file_path()
                shutil.copy2(state_file_path, backup_file_path)
                logger.debug(f"已创建状态文件备份: {backup_file_path}")

            # 将状态序列化到临时文件
            with open(temp_file_path, "w", encoding="utf-8") as temp_file:
                json.dump(
                    orchestration.to_dict(), temp_file, indent=2, ensure_ascii=False
                )

            # 原子性地重命名临时文件为正式文件
            shutil.move(str(temp_file_path), str(state_file_path))

            logger.info(f"状态已原子化保存到: {state_file_path}")
            return True

        except (OSError, IOError) as e:
            logger.error(f"保存状态文件失败: {e}")
            # 清理临时文件（如果存在）
            if temp_file_path.exists():
                temp_file_path.unlink()
            raise IOError(f"Failed to save state to {state_file_path}: {e}") from e
        except TypeError as e:
            logger.error(f"序列化状态对象失败: {e}")
            # 清理临时文件（如果存在）
            if temp_file_path.exists():
                temp_file_path.unlink()
            raise TypeError(f"Failed to serialize orchestration: {e}") from e

    def load_state(self) -> Optional[SearchOrchestration]:
        """加载状态

        从状态文件加载 SearchOrchestration 对象。
        如果状态文件不存在，返回 None。

        Returns:
            Optional[SearchOrchestration]: 加载的搜索编排对象，如果文件不存在则返回 None

        Raises:
            IOError: 文件读取失败
            ValueError: 反序列化失败
        """
        state_file_path = self.get_state_file_path()

        # 如果状态文件不存在，返回 None
        if not state_file_path.exists():
            logger.debug(f"状态文件不存在: {state_file_path}")
            return None

        try:
            # 从文件加载数据
            with open(state_file_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            # 反序列化为 SearchOrchestration 对象
            orchestration = SearchOrchestration.from_dict(data)

            logger.info(f"状态已从 {state_file_path} 加载")
            return orchestration

        except (OSError, IOError) as e:
            logger.error(f"读取状态文件失败: {e}")
            raise IOError(f"Failed to load state from {state_file_path}: {e}") from e
        except json.JSONDecodeError as e:
            logger.error(f"状态文件格式错误: {e}")
            raise ValueError(
                f"Invalid JSON in state file {state_file_path}: {e}"
            ) from e
        except ValueError as e:
            logger.error(f"反序列化状态对象失败: {e}")
            raise ValueError(f"Failed to deserialize orchestration: {e}") from e

    def load_state_with_fallback(self) -> Optional[SearchOrchestration]:
        """加载状态（带备份回退）

        首先尝试从主状态文件加载，如果失败则尝试从备份文件加载。

        Returns:
            Optional[SearchOrchestration]: 加载的搜索编排对象，如果都失败则返回 None
        """
        try:
            # 首先尝试从主文件加载
            return self.load_state()
        except (IOError, ValueError) as e:
            logger.warning(f"从主状态文件加载失败: {e}")

            # 如果启用了备份，尝试从备份文件加载
            if self.backup_enabled:
                backup_file_path = self.get_backup_file_path()
                if backup_file_path.exists():
                    try:
                        logger.info(f"尝试从备份文件恢复: {backup_file_path}")
                        with open(backup_file_path, "r", encoding="utf-8") as f:
                            data = json.load(f)
                        orchestration = SearchOrchestration.from_dict(data)
                        logger.info(f"状态已从备份文件 {backup_file_path} 恢复")
                        return orchestration
                    except Exception as backup_e:
                        logger.error(f"从备份文件恢复也失败了: {backup_e}")

            # 如果都失败了，返回 None
            logger.error("无法从主文件或备份文件加载状态")
            return None

    def state_file_exists(self) -> bool:
        """检查状态文件是否存在"""
        return self.get_state_file_path().exists()

    def backup_file_exists(self) -> bool:
        """检查备份文件是否存在"""
        return self.get_backup_file_path().exists()

    def remove_state_file(self) -> bool:
        """删除状态文件

        Returns:
            bool: 删除是否成功
        """
        state_file_path = self.get_state_file_path()
        if state_file_path.exists():
            try:
                state_file_path.unlink()
                logger.info(f"状态文件已删除: {state_file_path}")
                return True
            except OSError as e:
                logger.error(f"删除状态文件失败: {e}")
                return False
        return True  # 文件不存在也算删除成功

    def get_state_info(self) -> dict:
        """获取状态文件信息

        Returns:
            dict: 包含状态文件信息的字典
        """
        state_file_path = self.get_state_file_path()
        backup_file_path = self.get_backup_file_path()

        info = {
            "work_dir": str(self.work_dir),
            "state_file": self.state_file,
            "state_file_exists": state_file_path.exists(),
            "backup_enabled": self.backup_enabled,
            "backup_file_exists": backup_file_path.exists(),
        }

        if state_file_path.exists():
            stat = state_file_path.stat()
            info["state_file_size"] = stat.st_size
            info["state_file_mtime"] = stat.st_mtime

        if backup_file_path.exists():
            stat = backup_file_path.stat()
            info["backup_file_size"] = stat.st_size
            info["backup_file_mtime"] = stat.st_mtime

        return info


# 工厂函数
def create_state_manager(
    work_dir: str = ".",
    state_file: str = "orchestration.json",
    backup_enabled: bool = True,
) -> StateManager:
    """创建状态管理器

    Args:
        work_dir: 工作目录路径
        state_file: 状态文件名
        backup_enabled: 是否启用备份功能

    Returns:
        StateManager: 配置好的状态管理器实例
    """
    return StateManager(
        work_dir=work_dir, state_file=state_file, backup_enabled=backup_enabled
    )
