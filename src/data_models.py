"""
数据模型模块

为 Google Maps Grid Search 项目提供核心数据模型。
这些数据模型构成了新架构的基础，支持数据驱动的广度优先搜索。

基于 TECHNICAL_DESIGN.md 中的设计规范。
"""

import json
from datetime import datetime
from enum import Enum
from typing import Dict, List, Set, Optional, Any
from dataclasses import dataclass, field, asdict


class SearchStatus(Enum):
    """搜索状态枚举

    定义网格单元的各种可能状态，支持完整的生命周期跟踪。
    """

    PENDING = "pending"  # 待处理
    PROCESSING = "processing"  # 正在处理中
    SEARCH_COMPLETE = "search_complete"  # 搜索完成（无需细化）
    REFINEMENT_NEEDED = "refinement_needed"  # 需要细化
    REFINEMENT_COMPLETE = "refinement_complete"  # 细化完成
    FAILED = "failed"  # 处理失败


@dataclass
class GridCell:
    """网格单元数据模型

    封装单个网格单元的所有信息，包括位置、状态、层级关系和搜索结果。
    这是新架构中的核心数据结构，支持精确的状态跟踪和恢复。

    属性:
        cell_id: 全局唯一的网格单元标识符
        status: 当前搜索状态
        layer_id: 所属层级ID
        parent_id: 父网格单元ID（根单元为None）
        children_ids: 子网格单元ID列表
        center_lat: 中心点纬度
        center_lng: 中心点经度
        search_radius: 搜索半径（米）
        results_count: 找到的地点数量
        place_ids: 发现的地点ID集合
        api_calls_count: API调用次数
        created_at: 创建时间戳
        updated_at: 最后更新时间戳
        error_message: 错误信息（如果有）
        metadata: 额外的元数据字典
    """

    cell_id: str
    status: SearchStatus
    layer_id: int
    center_lat: float
    center_lng: float
    search_radius: float
    parent_id: Optional[str] = None
    children_ids: List[str] = field(default_factory=list)
    results_count: int = 0
    place_ids: Set[str] = field(default_factory=set)
    api_calls_count: int = 0
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """初始化后处理，确保数据一致性"""
        # 确保 cell_id 格式正确
        if not self.cell_id:
            self.cell_id = self._generate_cell_id()

        # 确保 place_ids 是 set 类型
        if isinstance(self.place_ids, list):
            self.place_ids = set(self.place_ids)

    def _generate_cell_id(self) -> str:
        """生成唯一的网格单元ID"""
        return f"L{self.layer_id}-{self.center_lat:.6f}-{self.center_lng:.6f}"

    def update_status(self, new_status: SearchStatus) -> None:
        """更新状态并记录更新时间"""
        self.status = new_status
        self.updated_at = datetime.now()

    def record_search_results(
        self, results_count: int, place_ids: Set[str], api_calls: int = 1
    ) -> None:
        """记录搜索结果"""
        self.results_count = results_count
        self.place_ids.update(place_ids)
        self.api_calls_count += api_calls
        self.updated_at = datetime.now()

    def add_child(self, child_id: str) -> None:
        """添加子网格单元"""
        if child_id not in self.children_ids:
            self.children_ids.append(child_id)
            self.updated_at = datetime.now()

    def set_error(self, error_message: str) -> None:
        """设置错误状态"""
        self.error_message = error_message
        self.status = SearchStatus.FAILED
        self.updated_at = datetime.now()

    def needs_refinement(self) -> bool:
        """判断是否需要细化"""
        return self.status == SearchStatus.REFINEMENT_NEEDED

    def is_complete(self) -> bool:
        """判断是否已完成处理"""
        return self.status in [
            SearchStatus.SEARCH_COMPLETE,
            SearchStatus.REFINEMENT_COMPLETE,
        ]

    def is_leaf(self) -> bool:
        """判断是否为叶子节点（没有子节点）"""
        return len(self.children_ids) == 0

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，支持JSON序列化"""
        data = asdict(self)

        # 转换枚举为字符串
        data["status"] = self.status.value

        # 转换集合为列表以便序列化
        data["place_ids"] = list(self.place_ids)

        # 转换datetime为ISO格式字符串
        data["created_at"] = self.created_at.isoformat()
        data["updated_at"] = self.updated_at.isoformat()

        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "GridCell":
        """从字典创建GridCell对象"""
        # 转换状态字符串为枚举
        if isinstance(data["status"], str):
            data["status"] = SearchStatus(data["status"])

        # 转换时间字符串为datetime对象
        data["created_at"] = datetime.fromisoformat(data["created_at"])
        data["updated_at"] = datetime.fromisoformat(data["updated_at"])

        # 确保place_ids是集合类型
        if isinstance(data["place_ids"], list):
            data["place_ids"] = set(data["place_ids"])

        return cls(**data)

    def get_summary(self) -> Dict[str, Any]:
        """获取网格单元的摘要信息"""
        return {
            "cell_id": self.cell_id,
            "status": self.status.value,
            "layer_id": self.layer_id,
            "center": {"lat": self.center_lat, "lng": self.center_lng},
            "results_count": self.results_count,
            "api_calls_count": self.api_calls_count,
            "has_children": len(self.children_ids) > 0,
            "error": self.error_message is not None,
        }


@dataclass
class SearchOrchestration:
    """搜索编排数据模型

    这是新架构中的"单一事实来源"，包含整个搜索任务的完整状态。
    所有组件都围绕这个对象进行操作，实现逻辑与状态的完全解耦。

    属性:
        task_id: 任务唯一标识符
        place_type: 搜索的地点类型
        location: 搜索位置
        status: 整体任务状态
        cells: 所有网格单元的字典，以cell_id为键
        layers: 按层级组织的网格单元ID字典
        config: 搜索配置信息
        metrics: 搜索性能指标
        created_at: 创建时间
        updated_at: 最后更新时间
        current_layer: 当前正在处理的层级
        completed_layers: 已完成的层级列表
    """

    task_id: str
    place_type: str
    location: str
    cells: Dict[str, GridCell] = field(default_factory=dict)
    layers: Dict[int, List[str]] = field(default_factory=dict)
    config: Dict[str, Any] = field(default_factory=dict)
    metrics: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    current_layer: int = 0
    completed_layers: List[int] = field(default_factory=list)

    def __post_init__(self):
        """初始化后处理"""
        # 如果没有task_id，生成一个
        if not self.task_id:
            self.task_id = self._generate_task_id()

        # 初始化默认配置
        if not self.config:
            self.config = self._get_default_config()

        # 初始化默认指标
        if not self.metrics:
            self.metrics = self._get_default_metrics()

    def _generate_task_id(self) -> str:
        """生成唯一的任务ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{self.place_type}-{self.location}-{timestamp}"

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "max_refinement_levels": 4,
            "initial_radius": 1000,
            "min_refinement_radius": 50,
            "max_api_calls_per_cell": 3,
            "results_threshold_for_refinement": 20,
            "grid_overlap_factor": 0.8,
        }

    def _get_default_metrics(self) -> Dict[str, Any]:
        """获取默认性能指标"""
        return {
            "total_api_calls": 0,
            "total_places_found": 0,
            "total_cells_processed": 0,
            "total_cells_created": 0,
            "processing_time_seconds": 0,
            "layers_completed": 0,
            "failed_cells": 0,
        }

    def add_cell(self, cell: GridCell) -> None:
        """添加网格单元"""
        self.cells[cell.cell_id] = cell

        # 添加到层级索引
        if cell.layer_id not in self.layers:
            self.layers[cell.layer_id] = []

        if cell.cell_id not in self.layers[cell.layer_id]:
            self.layers[cell.layer_id].append(cell.cell_id)

        # 更新指标
        self.metrics["total_cells_created"] = len(self.cells)
        self.updated_at = datetime.now()

    def get_cell(self, cell_id: str) -> Optional[GridCell]:
        """获取网格单元"""
        return self.cells.get(cell_id)

    def get_layer_cells(self, layer_id: int) -> List[GridCell]:
        """获取指定层级的所有网格单元"""
        cell_ids = self.layers.get(layer_id, [])
        return [self.cells[cell_id] for cell_id in cell_ids if cell_id in self.cells]

    def get_pending_cells(self, layer_id: Optional[int] = None) -> List[GridCell]:
        """获取待处理的网格单元"""
        target_layer = layer_id if layer_id is not None else self.current_layer
        cells = self.get_layer_cells(target_layer)
        return [cell for cell in cells if cell.status == SearchStatus.PENDING]

    def get_processing_cells(self, layer_id: Optional[int] = None) -> List[GridCell]:
        """获取正在处理的网格单元"""
        target_layer = layer_id if layer_id is not None else self.current_layer
        cells = self.get_layer_cells(target_layer)
        return [cell for cell in cells if cell.status == SearchStatus.PROCESSING]

    def get_cells_needing_refinement(self) -> List[GridCell]:
        """获取需要细化的网格单元"""
        return [cell for cell in self.cells.values() if cell.needs_refinement()]

    def mark_layer_complete(self, layer_id: int) -> None:
        """标记层级为已完成"""
        if layer_id not in self.completed_layers:
            self.completed_layers.append(layer_id)
            self.metrics["layers_completed"] = len(self.completed_layers)
            self.updated_at = datetime.now()

    def update_metrics(self, **kwargs) -> None:
        """更新性能指标"""
        for key, value in kwargs.items():
            if key in self.metrics:
                self.metrics[key] = value

        # 计算衍生指标
        self.metrics["total_cells_processed"] = sum(
            1 for cell in self.cells.values() if cell.is_complete()
        )
        self.metrics["failed_cells"] = sum(
            1 for cell in self.cells.values() if cell.status == SearchStatus.FAILED
        )
        self.metrics["total_places_found"] = len(self.get_all_place_ids())
        self.metrics["total_api_calls"] = sum(
            cell.api_calls_count for cell in self.cells.values()
        )

        self.updated_at = datetime.now()

    def get_all_place_ids(self) -> Set[str]:
        """获取所有发现的地点ID"""
        all_place_ids = set()
        for cell in self.cells.values():
            all_place_ids.update(cell.place_ids)
        return all_place_ids

    def get_summary(self) -> Dict[str, Any]:
        """获取搜索任务的摘要信息"""
        return {
            "task_id": self.task_id,
            "place_type": self.place_type,
            "location": self.location,
            "status": self.get_overall_status(),
            "current_layer": self.current_layer,
            "completed_layers": len(self.completed_layers),
            "total_layers": len(self.layers),
            "total_cells": len(self.cells),
            "metrics": self.metrics,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }

    def get_overall_status(self) -> str:
        """获取整体任务状态"""
        if not self.cells:
            return "initialized"

        total_cells = len(self.cells)
        completed_cells = sum(1 for cell in self.cells.values() if cell.is_complete())
        failed_cells = sum(
            1 for cell in self.cells.values() if cell.status == SearchStatus.FAILED
        )

        if failed_cells == total_cells:
            return "failed"
        elif completed_cells == total_cells:
            return "completed"
        elif completed_cells > 0:
            return "in_progress"
        else:
            return "pending"

    def is_complete(self) -> bool:
        """判断整个搜索任务是否完成"""
        return self.get_overall_status() == "completed"

    def has_pending_work(self) -> bool:
        """判断是否还有待处理的工作"""
        return len(self.get_pending_cells()) > 0

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，支持JSON序列化"""
        return {
            "task_id": self.task_id,
            "place_type": self.place_type,
            "location": self.location,
            "cells": {cell_id: cell.to_dict() for cell_id, cell in self.cells.items()},
            "layers": self.layers,
            "config": self.config,
            "metrics": self.metrics,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "current_layer": self.current_layer,
            "completed_layers": self.completed_layers,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "SearchOrchestration":
        """从字典创建SearchOrchestration对象"""
        # 转换cells字典
        cells_data = data.pop("cells", {})
        cells = {
            cell_id: GridCell.from_dict(cell_data)
            for cell_id, cell_data in cells_data.items()
        }

        # 转换layers字典的键为整数
        layers_data = data.pop("layers", {})
        layers = {int(layer_id): cell_ids for layer_id, cell_ids in layers_data.items()}

        # 转换时间字符串为datetime对象
        data["created_at"] = datetime.fromisoformat(data["created_at"])
        data["updated_at"] = datetime.fromisoformat(data["updated_at"])

        # 创建对象
        orchestration = cls(**data)
        orchestration.cells = cells
        orchestration.layers = layers

        return orchestration

    def save_to_file(self, file_path: str) -> None:
        """保存到JSON文件"""
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)

    @classmethod
    def load_from_file(cls, file_path: str) -> "SearchOrchestration":
        """从JSON文件加载"""
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
        return cls.from_dict(data)


def create_root_cell(
    center_lat: float,
    center_lng: float,
    search_radius: float,
    place_type: str,
    layer_id: int = 0,
    parent_id: Optional[str] = None,
) -> GridCell:
    """创建根网格单元的工厂函数

    封装 GridCell 的创建逻辑，提供合理的默认值和参数验证。

    Args:
        center_lat: 中心点纬度
        center_lng: 中心点经度
        search_radius: 搜索半径（米）
        place_type: 地点类型
        layer_id: 层级ID，默认为0
        parent_id: 父单元ID，默认为None（表示根单元）

    Returns:
        GridCell: 完全初始化的网格单元对象
    """
    return GridCell(
        cell_id="",  # 空字符串，__post_init__会自动生成
        status=SearchStatus.PENDING,
        layer_id=layer_id,
        center_lat=center_lat,
        center_lng=center_lng,
        search_radius=search_radius,
        parent_id=parent_id,
        metadata={"place_type": place_type, "created_by": "factory_function"},
    )


def create_search_orchestration(
    place_type: str,
    location: str,
    center_lat: float,
    center_lng: float,
    search_radius: float,
    config: Optional[Dict[str, Any]] = None,
) -> SearchOrchestration:
    """创建搜索编排对象的工厂函数

    封装 SearchOrchestration 的创建逻辑，提供合理的默认配置，
    并自动创建初始的根网格单元。

    Args:
        place_type: 搜索的地点类型（如 "restaurant", "cafe" 等）
        location: 搜索位置（如 "Berlin, Germany"）
        center_lat: 中心点纬度
        center_lng: 中心点经度
        search_radius: 搜索半径（米）
        config: 可选的配置字典，如果不提供则使用默认配置

    Returns:
        SearchOrchestration: 完全初始化的搜索编排对象，包含一个根网格单元
    """
    # 合并默认配置和用户提供的配置
    default_config = {
        "max_refinement_levels": 4,
        "initial_radius": search_radius,
        "min_refinement_radius": 50,
        "max_api_calls_per_cell": 3,
        "results_threshold_for_refinement": 20,
        "grid_overlap_factor": 0.8,
    }

    if config:
        default_config.update(config)

    # 创建搜索编排对象
    orchestration = SearchOrchestration(
        task_id="",  # 空字符串，__post_init__会自动生成
        place_type=place_type,
        location=location,
        config=default_config,
    )

    # 创建根网格单元并添加到编排对象中
    root_cell = create_root_cell(center_lat, center_lng, search_radius, place_type)
    orchestration.add_cell(root_cell)

    return orchestration


def create_search_orchestration_with_grid(
    place_type: str,
    location: str,
    grid_points,
    search_radius: float,
    config: Optional[Dict[str, Any]] = None,
) -> SearchOrchestration:
    """创建包含完整网格的搜索编排对象

    专门为 main.py 设计的工厂函数，创建包含多个网格单元的完整初始化编排对象。

    Args:
        place_type: 搜索的地点类型（如 "restaurant", "cafe" 等）
        location: 搜索位置（如 "Berlin, Germany"）
        grid_points: 网格点列表，每个点是 (lat, lng) 元组
        search_radius: 搜索半径（米）
        config: 可选的配置字典，如果不提供则使用默认配置

    Returns:
        SearchOrchestration: 完全初始化的搜索编排对象，包含所有网格单元
    """
    # 合并默认配置和用户提供的配置
    default_config = {
        "max_refinement_levels": 4,
        "initial_radius": search_radius,
        "min_refinement_radius": 50,
        "max_api_calls_per_cell": 3,
        "results_threshold_for_refinement": 20,
        "grid_overlap_factor": 0.8,
    }

    if config:
        default_config.update(config)

    # 创建搜索编排对象
    orchestration = SearchOrchestration(
        task_id="",  # 空字符串，__post_init__会自动生成
        place_type=place_type,
        location=location,
        config=default_config,
    )

    # 创建所有网格单元并添加到编排对象中
    for lat, lng in grid_points:
        cell = create_root_cell(lat, lng, search_radius, place_type)
        orchestration.add_cell(cell)

    return orchestration
