"""
日志配置模块

为Google Maps Grid Search项目提供统一的日志配置和管理。
支持不同级别的日志输出、格式化配置和文件输出。
"""

import logging
import os
import sys
from typing import Optional


class LoggerFactory:
    """日志工厂类，负责创建和配置日志记录器"""

    _configured = False
    _log_level = logging.INFO
    _log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    _file_handler = None

    @classmethod
    def configure(
        cls,
        log_level: str = "INFO",
        log_file: Optional[str] = None,
        log_format: Optional[str] = None,
    ) -> None:
        """
        配置全局日志系统

        Args:
            log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            log_file: 日志文件路径，如果为None则只输出到控制台
            log_format: 日志格式字符串
        """
        if cls._configured:
            return

        # 设置日志级别
        cls._log_level = getattr(logging, log_level.upper(), logging.INFO)

        # 设置日志格式
        if log_format:
            cls._log_format = log_format

        # 配置根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(cls._log_level)

        # 清除已有的处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 创建控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(cls._log_level)
        console_formatter = logging.Formatter(cls._log_format)
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)

        # 如果指定了日志文件，创建文件处理器
        if log_file:
            # 确保日志文件目录存在
            log_dir = os.path.dirname(log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)

            file_handler = logging.FileHandler(log_file, encoding="utf-8")
            file_handler.setLevel(cls._log_level)
            file_formatter = logging.Formatter(cls._log_format)
            file_handler.setFormatter(file_formatter)
            root_logger.addHandler(file_handler)
            cls._file_handler = file_handler

        cls._configured = True

    @classmethod
    def get_logger(cls, name: str) -> logging.Logger:
        """
        获取指定名称的日志记录器

        Args:
            name: 日志记录器名称，通常使用模块名

        Returns:
            配置好的日志记录器实例
        """
        if not cls._configured:
            cls.configure()

        return logging.getLogger(name)

    @classmethod
    def set_level(cls, level: str) -> None:
        """
        动态设置日志级别

        Args:
            level: 日志级别字符串
        """
        if not cls._configured:
            cls.configure()

        log_level = getattr(logging, level.upper(), logging.INFO)
        logging.getLogger().setLevel(log_level)

        # 更新所有处理器的级别
        for handler in logging.getLogger().handlers:
            handler.setLevel(log_level)

    @classmethod
    def add_file_handler(cls, log_file: str) -> None:
        """
        添加文件处理器

        Args:
            log_file: 日志文件路径
        """
        if not cls._configured:
            cls.configure()

        if cls._file_handler and cls._file_handler.baseFilename == log_file:
            return  # 已经存在相同的文件处理器

        # 确保日志文件目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)

        file_handler = logging.FileHandler(log_file, encoding="utf-8")
        file_handler.setLevel(cls._log_level)
        file_formatter = logging.Formatter(cls._log_format)
        file_handler.setFormatter(file_formatter)
        logging.getLogger().addHandler(file_handler)
        cls._file_handler = file_handler


def get_logger(name: str) -> logging.Logger:
    """
    便捷函数：获取日志记录器

    Args:
        name: 日志记录器名称

    Returns:
        配置好的日志记录器实例
    """
    return LoggerFactory.get_logger(name)


def configure_logging(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    log_format: Optional[str] = None,
) -> None:
    """
    便捷函数：配置日志系统

    Args:
        log_level: 日志级别
        log_file: 日志文件路径
        log_format: 日志格式字符串
    """
    LoggerFactory.configure(log_level, log_file, log_format)
