[project]
name = "google-maps-grid-search"
version = "0.1.0"
description = "A Python tool for extracting place data from Google Maps using adaptive grid-based search"
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "requests>=2.25.0",
    "python-dotenv>=0.19.0",
    "folium>=0.12.0",
    "psutil>=7.0.0",
    "flake8>=5.0.4",
]

[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[dependency-groups]
dev = [
    "pytest>=8.3.5",
    "pytest-cov>=5.0.0",
    "pytest-mock>=3.14.1",
]

[project.scripts]
grid-search = "src.main:main"

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-mock>=3.6.0",
    "pytest-cov>=4.0.0",
    "black>=22.0.0",
    "flake8>=5.0.0",
]
