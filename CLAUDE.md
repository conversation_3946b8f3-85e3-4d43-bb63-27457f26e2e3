# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is a Python-based tool for extracting place data from Google Maps using the Google Places API. It uses an adaptive grid-based approach to efficiently find and extract data about any type of establishment in a geographic area.

🎉 **IMPORTANT**: This project has completed its major architectural refactoring! The data-driven BFS architecture has fully replaced the old recursive DFS architecture. The project is now production-ready with comprehensive test coverage (91/91 tests passing).

## Technical Foundation

### Primary Reference Documents
- **`doc/TECHNICAL_DESIGN.md`**: Contains the complete technical design for the data-driven BFS architecture
- **`doc/REFACTORING_PLAN.md`**: Provided the detailed, executable task list for implementation (now complete)

### Current State: Refactoring Complete ✅

The project has successfully transitioned from:
- **FROM**: Monolithic, recursive DFS architecture with poor reliability and weak recovery
- **TO**: Modular, data-driven BFS architecture with atomic state management and parallel processing capability

**Current Progress**: 
- ✅ Technical design documentation completed
- ✅ Detailed refactoring plan completed
- ✅ All refactoring tasks completed (100%)
- ✅ New architecture is production-ready

## Architecture Transformation ✅

### Based on TECHNICAL_DESIGN.md

The architectural transformation addressed critical issues and is now complete:

**Core Problems with Legacy Architecture (SOLVED):**
- ✅ **Poor Reliability**: Recursive DFS caused stack overflow in dense areas → Non-recursive BFS design
- ✅ **Weak Recovery**: Non-atomic progress persistence led to data corruption → Atomic state management
- ✅ **Limited Scalability**: Could not be parallelized due to recursive design → BFS foundation for parallelization
- ✅ **Zero Observability**: No real-time monitoring or debugging capabilities → Structured state objects

**Target Architecture (Now Active - Data-Driven BFS):**
```
┌─────────────────────────────────────┐
│           Application Layer          │
│             (main.py)               │
├─────────────────────────────────────┤
│      Data Models & State            │
│    (data_models.py, state_manager)  │
├─────────────────────────────────────┤
│     Processing Engine Layer         │
│         (grid_engine.py)            │
├─────────────────────────────────────┤
│        Infrastructure Layer         │
│    (api/, visualization, report_)   │
└─────────────────────────────────────┘
```

## Development Guidelines

### Working with This Project

**Prerequisite Reading**: 
All major refactoring is complete. The following documents are for historical reference:
1. `doc/TECHNICAL_DESIGN.md` - Technical foundation (IMPLEMENTED)
2. `doc/REFACTORING_PLAN.md` - Implementation sequence (COMPLETED)

**Working with Current Code**:
- The new architecture is fully implemented and production-ready
- All new development should leverage the data-driven design
- The system provides atomic state management and interruption recovery
- Follow existing code patterns and conventions

### Code Conventions
- Use dataclasses for all data models
- Implement proper error handling and logging
- Follow the principle of single responsibility
- Maintain separation of concerns between layers
- All state persistence is atomic using "write temp file then rename" strategy
- Use structured logging with the centralized logging_config.py
- Prefer composition over inheritance for modularity

## Development Commands

### Environment Setup
Create a `.env` file with your Google Maps API key:
```
GOOGLE_MAPS_API_KEY=your_api_key_here
```

### Installation and Dependencies
```bash
# Install with pip
pip install .

# Or use uv for reproducible builds
uv sync

# Lock dependencies (already completed)
uv lock
```

### Code Quality and Linting
```bash
# Format code with black
black src/ tests/

# Lint code with flake8
flake8 src/ tests/
```

### Running Tests

The project has comprehensive test coverage (91/91 tests passing):

```bash
# Run all tests
python -m pytest tests/ -v

# Run specific module tests
python -m pytest tests/test_data_models.py -v
python -m pytest tests/test_grid_engine.py -v
python -m pytest tests/test_state_manager.py -v
python -m pytest tests/test_integration.py -v

# Run single test
python -m pytest tests/test_data_models.py::TestSearchOrchestration::test_get_pending_cells -v

# Generate coverage report
python -m pytest tests/ -v --cov=src --cov-report=term-missing --cov-report=html

# Run performance benchmarks
python -m tests/test_performance
```

### Running the Application

#### Production-Ready Architecture (Active)
```bash
# Basic search using new data-driven architecture
python -m src.main --location "Berlin, Germany" --place-type "restaurant"

# Dry run (no API calls)
python -m src.main --location "Berlin, Germany" --place-type "restaurant" --dry-run

# Resume from interrupted search (new capability)
python -m src.main --location "Berlin, Germany" --place-type "restaurant"

# Generate visualization maps
python -m src.main --location "Berlin, Germany" --place-type "restaurant" --visualize

# Limit API calls
python -m src.main --location "Berlin, Germany" --place-type "restaurant" --max-calls 100
```

### Testing Commands

#### Test Areas Available
- **alexanderplatz** - Dense urban area
- **tiergarten** - Sparse park area  
- **kreuzberg** - Mixed density area
- **friedrichstrasse** - Commercial district

#### Test Commands
```bash
# Completely free dry run without API calls
python -m src.main --test-area alexanderplatz --dry-run

# Test with visualization
python -m src.main --test-area kreuzberg --dry-run --visualize

# Run all tests
python -m pytest tests/ -v
```

## File Structure (Current Active Architecture)

```
src/
├── main.py                    # Application entry point (uses new architecture)
├── config.py                  # Configuration (reused)
├── grid_algorithms.py         # Geo algorithms (reused)
├── data_models.py             # Core data models (per technical design)
├── state_manager.py           # Atomic state management (per technical design)
├── grid_engine.py             # BFS processing engine (per technical design)
├── api_client.py              # API client (reused)
├── api/                       # API layer (reused)
│   ├── interface.py
│   ├── google_client.py
│   └── mock_client.py
├── visualization.py           # Adapted for new data structures
├── report_generator.py        # Adapted for new data structures
├── app_state.py               # Application state management
└── test_utils.py              # Test utilities (reused)

# Legacy files have been REMOVED:
# ├── context.py (DELETED)
# ├── grid_processor.py (DELETED)
# ├── search_processor.py (DELETED)
# └── progress_manager.py (DELETED)
```

## Output File Structure (Current)

- `orchestration.json`: Complete search state (atomic, serializable)
- `found_place_ids.json`: Found place IDs
- `summary.csv`: Enhanced CSV summaries with metadata
- `visualization_map.html`: Updated visualization maps with layered data
- `map_data.json`: Map data for future use
- `detailed_place_data/`: Detailed place information (JSON format)

## Dependencies

### Core Dependencies
- `requests>=2.25.0` - HTTP requests to Google Maps API
- `python-dotenv>=0.19.0` - Environment variable management
- `folium>=0.12.0` - Interactive map generation

### Testing Dependencies
- `pytest>=8.3.5` - Comprehensive testing framework
- `pytest-cov>=5.0.0` - Code coverage reporting
- `pytest-mock>=3.14.1` - Mocking capabilities

### Development Dependencies
- `black>=22.0.0` - Code formatting
- `flake8>=5.0.0` - Code linting
- `psutil>=7.0.0` - System monitoring (for performance testing)

### Package Management
The project uses `pyproject.toml` for dependency management with `uv.lock` for reproducible builds.

### Entry Point
The package console script entry point:
```bash
grid-search # runs src.main:main
```

### Configuration
The project uses `algorithm_config.json` for core algorithm parameters:
- `search.max_radius`: 50000 (maximum search radius)
- `search.subdivision_threshold`: 45 (subdivision threshold)
- `search.max_refinement_levels`: 4 (maximum refinement levels)
- `api.nearby_search_single_page_size`: 20 (results per page)

## Key Features of Completed Architecture ✅

1. ✅ **Reliability**: Atomic state management prevents data corruption
2. ✅ **Recovery**: Exact interruption recovery capability
3. ✅ **Scalability**: BFS foundation enables future parallelization
4. ✅ **Observability**: Structured state objects for monitoring
5. ✅ **Test Coverage**: 91/91 tests passing
6. ✅ **Modularity**: Clean separation of concerns
7. ✅ **Maintainability**: Well-documented and tested codebase

## Core Architecture Patterns

### Data-Driven Design
- `SearchOrchestration` is the single source of truth for all search state
- All operations are atomic and idempotent
- State is completely serializable for persistence and recovery

### Layered Processing
- BFS-based layer processing instead of recursive DFS
- Each layer represents a level of grid refinement
- Parent-child relationships maintain grid hierarchy

### State Management Strategy
- Atomic saves using "write temp file then rename" pattern
- Automatic backups prevent data loss
- Graceful fallback mechanisms for corrupted state

### Error Handling Philosophy
- Fail-fast with detailed error messages
- Recovery-oriented operations
- Comprehensive logging at all levels

## Critical Development Notes

### When Adding New Features
1. **Always create tests first** - maintain 100% test coverage
2. **Use existing data models** - extend `SearchOrchestration` and `GridCell`
3. **Follow atomic state patterns** - use `StateManager` for all persistence
4. **Maintain layer separation** - respect the architectural layers
5. **Update documentation** - keep README.md and CLAUDE.md in sync

### When Debugging Issues
1. **Check orchestration.json** - contains complete search state
2. **Review structured logs** - use centralized logging system
3. **Run specific tests** - isolate issues with targeted test execution
4. **Use performance benchmarks** - identify performance regressions

### Performance Considerations
- New architecture trades slight performance overhead for reliability
- BFS processing is more memory-efficient than recursive DFS
- State serialization overhead is minimal compared to API costs
- Parallel processing capability is built into the design

---
*This project is now production-ready with its data-driven BFS architecture!*