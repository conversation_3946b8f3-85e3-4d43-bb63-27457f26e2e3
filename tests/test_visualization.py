"""
可视化模块单元测试

为 Google Maps Grid Search 项目的可视化模块提供单元测试。
测试覆盖从SearchOrchestration对象生成可视化等核心功能。

基于 TECHNICAL_DESIGN.md 中的测试策略设计。
"""

import unittest
import tempfile
from pathlib import Path

from src.data_models import (
    GridCell,
    SearchStatus,
    create_search_orchestration,
)
from src.visualization import generate_visualization_from_orchestration


class TestVisualization(unittest.TestCase):
    """测试可视化模块"""

    def setUp(self):
        """测试前准备"""
        # 创建临时目录用于测试输出
        self.temp_dir = Path(tempfile.mkdtemp())

        # 创建测试编排对象
        self.test_orchestration = create_search_orchestration(
            place_type="restaurant",
            location="Berlin, Germany",
            center_lat=52.52,
            center_lng=13.405,
            search_radius=1000.0,
        )

        # 添加一些测试单元
        root_cell = self.test_orchestration.get_cell(
            self.test_orchestration.layers[0][0]
        )
        root_cell.update_status(SearchStatus.SEARCH_COMPLETE)
        root_cell.record_search_results(
            5, {"place1", "place2", "place3", "place4", "place5"}
        )

        # 添加一个需要细化的单元
        child_cell = GridCell(
            cell_id="L1-52.521000-13.406000",
            status=SearchStatus.REFINEMENT_NEEDED,
            layer_id=1,
            center_lat=52.521,
            center_lng=13.406,
            search_radius=500.0,
            parent_id=root_cell.cell_id,
        )
        self.test_orchestration.add_cell(child_cell)

    def tearDown(self):
        """测试后清理"""
        # 清理临时目录
        import shutil

        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)

    def test_generate_visualization_from_orchestration(self):
        """测试从编排对象生成可视化"""
        # 创建输出文件路径
        output_file = self.temp_dir / "test_visualization.html"

        # 生成可视化
        try:
            generate_visualization_from_orchestration(
                self.test_orchestration, str(output_file)
            )

            # 验证文件是否创建（如果folium可用）
            # 注意：在测试环境中folium可能不可用，所以我们会检查函数是否正常执行
            # 如果folium不可用，函数会打印消息并返回，不会抛出异常
            self.assertTrue(True, "函数执行完成")
        except Exception as e:
            # 如果folium不可用，这是预期的
            if "Folium not installed" in str(e) or "folium" in str(e).lower():
                self.assertTrue(True, "Folium不可用是预期的")
            else:
                raise e

    def test_generate_visualization_content(self):
        """测试生成的可视化文件内容"""
        # 创建输出文件路径
        output_file = self.temp_dir / "test_visualization_content.html"

        # 生成可视化
        try:
            generate_visualization_from_orchestration(
                self.test_orchestration, str(output_file)
            )

            # 验证函数是否正常执行
            self.assertTrue(True, "函数执行完成")

            # 如果文件存在，验证内容
            if output_file.exists():
                content = output_file.read_text(encoding="utf-8")
                # 验证是否包含关键元素
                self.assertIsInstance(content, str)
                # 检查是否包含folium相关的标记
                if "folium" in content.lower() or "leaflet" in content.lower():
                    self.assertTrue(True, "包含地图相关元素")
        except Exception as e:
            # 如果folium不可用，这是预期的
            if "Folium not installed" in str(e) or "folium" in str(e).lower():
                self.assertTrue(True, "Folium不可用是预期的")
            else:
                raise e

    def test_generate_visualization_with_stats(self):
        """测试从编排对象生成可视化（带统计信息）"""
        # 创建输出文件路径
        output_file = self.temp_dir / "test_visualization_with_stats.html"

        # 准备搜索边界（模拟柏林的边界）
        search_bounds = (52.4800, 13.3400, 52.5600, 13.4700)  # Berlin bounds

        # 生成可视化
        try:
            generate_visualization_from_orchestration(
                self.test_orchestration,
                str(output_file),
                search_bounds=search_bounds,
                is_final=True,
            )

            # 验证函数是否正常执行
            self.assertTrue(True, "函数执行完成")
        except Exception as e:
            # 如果folium不可用，这是预期的
            if "Folium not installed" in str(e) or "folium" in str(e).lower():
                self.assertTrue(True, "Folium不可用是预期的")
            else:
                raise e


if __name__ == "__main__":
    unittest.main()
