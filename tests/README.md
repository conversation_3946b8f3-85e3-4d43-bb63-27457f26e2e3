# 测试套件说明

本目录包含了重构后的 Google Maps Grid Search 项目测试套件。

## 🎯 重构后的测试状态

### ✅ 核心模块测试（74个测试全部通过）
- `test_api_client.py` - 重构后的API客户端测试 (8个测试)
- `test_data_models.py` - 数据模型测试 (37个测试)
- `test_grid_algorithms.py` - 网格算法测试 (6个测试)
- `test_state_manager.py` - 状态管理器测试 (20个测试)
- `test_visualization.py` - 可视化模块测试 (3个测试)

### 🔧 需要更新的测试
- `test_grid_engine.py` - 需要适配重构后的GridEngine接口
- `test_integration.py` - 简化版集成测试

### 🗑️ 已删除的测试（针对已删除的模块）
- ~~`test_report_generator.py`~~ - report_generator.py已删除
- ~~`test_visualization_exceptions.py`~~ - visualization_exceptions.py已删除
- ~~`test_visualization_factory.py`~~ - visualization_factory.py已删除
- ~~`test_visualization_event_handler.py`~~ - visualization_event_handler.py已删除

### 📊 测试覆盖率
- **核心功能**: 100% 测试通过
- **API客户端**: 完全重构并测试通过
- **数据模型**: 保持完整测试覆盖
- **状态管理**: 完整测试覆盖
- **可视化**: 基础功能测试通过

## 运行测试

### 运行核心模块测试（推荐）
```bash
# 运行所有核心模块测试（74个测试全部通过）
python -m pytest tests/test_api_client.py tests/test_data_models.py tests/test_grid_algorithms.py tests/test_state_manager.py tests/test_visualization.py -v
```

### 运行所有测试
```bash
# 注意：包含一些需要更新的测试
python -m pytest tests/ -v
```

### 运行特定测试
```bash
# 运行单元测试
python -m pytest tests/test_data_models.py -v

# 运行集成测试
python -m pytest tests/test_integration.py -v

# 运行性能测试
python -m tests/test_performance
```

## 性能基准测试

性能基准测试用于对比新旧架构的性能表现。

### 运行性能测试
```bash
# 运行完整的性能基准测试
python -m tests/test_performance

# 指定测试区域
python -m tests/test_performance --test-areas alexanderplatz tiergarten

# 指定迭代次数
python -m tests/test_performance --iterations 5

# 指定输出目录
python -m tests/test_performance --output-dir my_benchmark_results
```

### 测试指标
1. **处理时间** - 总执行时间
2. **内存使用** - 峰值内存占用
3. **API调用次数** - 总API调用次数
4. **地点发现数量** - 发现的唯一地点ID数量

### 测试区域
- `alexanderplatz` - 密集城区
- `tiergarten` - 稀疏公园区域
- `kreuzberg` - 混合密度区域

测试结果将保存在 `benchmark_results` 目录中，包括详细的JSON数据和Markdown格式的报告。