import math
from src.grid_algorithms import (
    haversine_distance,
    meters_to_lat_degrees,
    meters_to_lng_degrees,
    calculate_max_distance_meters,
    generate_grid_points,
    calculate_search_parameters,
)


def test_haversine_distance():
    """
    测试 Haversine 距离计算算法。

    Haversine 公式用于计算球面上两点之间的距离，
    是地理计算中的标准算法。
    """
    # 测试用例1: 短距离（柏林内部）
    # 坐标: (52.5200, 13.4050) 到 (52.5201, 13.4051)
    # 预期距离约13.5米
    lat1, lon1 = 52.5200, 13.4050  # 柏林中心
    lat2, lon2 = 52.5201, 13.4051  # 附近点
    expected_distance = 13.5  # 米，约值
    actual_distance = haversine_distance(lat1, lon1, lat2, lon2)
    assert math.isclose(actual_distance, expected_distance, rel_tol=0.1)

    # 测试用例2: 长距离（柏林到纽约）
    # 跨越大西洋的距离，预期约6380公里
    lat3, lon3 = 40.7128, -74.0060  # 纽约
    expected_distance_km = 6380  # 公里，约值
    actual_distance_km = haversine_distance(lat1, lon1, lat3, lon3) / 1000
    assert math.isclose(actual_distance_km, expected_distance_km, rel_tol=0.01)

    # 测试用例3: 零距离（相同点）
    # 相同坐标应该返回0距离
    assert haversine_distance(lat1, lon1, lat1, lon1) == 0


def test_meters_to_lat_degrees():
    """
    测试米到纬度度数的转换。

    纬度度数到米的转换在全球范围内是恒定的，
    因为纬度线是平行的。
    1度纬度约等于111.32公里。
    """
    meters = 111320  # 111.32公里
    expected_degrees = 1.0  # 1度纬度
    actual_degrees = meters_to_lat_degrees(meters)
    assert math.isclose(actual_degrees, expected_degrees, rel_tol=1e-5)


def test_meters_to_lng_degrees():
    """
    测试米到经度度数的转换。

    经度度数到米的转换依赖于纬度，
    因为经度线在赤道处最宽，向两极收缩。
    在赤道上：1度经度 ≈ 111.32公里
    在60度纬度：1度经度 ≈ 55.66公里（需要两倍的距离）
    """
    meters = 111320  # 111.32公里

    # 在赤道（0度纬度）上，1度经度约等于111.32公里
    actual_equator = meters_to_lng_degrees(meters, 0)
    expected_equator = 1.0
    assert math.isclose(actual_equator, expected_equator, rel_tol=1e-5)

    # 在60度纬度上，由于余弦效应，需要两倍的距离才能得到1度
    # cos(60°) = 0.5, 所以需要 111.32 / 0.5 = 222.64公里来得到1度
    # 因此111.32公里应该得到0.5度
    actual_60_deg = meters_to_lng_degrees(meters, 60)
    expected_60_deg = 2.0  # 111.32 / (111.32 * cos(60°)) = 1 / 0.5 = 2
    assert math.isclose(actual_60_deg, expected_60_deg, rel_tol=1e-5)


def test_calculate_max_distance_meters():
    """
    测试从边界框中心到角落的最大距离计算。

    这个函数计算从边界框中心点到四个角落的最大距离，
    用于确定搜索区域的半径。
    """
    # 测试用例1: 赤道附近的1度x1度正方形
    # 对角线距离 ≈ √(111.32² + 111.32²) ≈ 157.43公里
    # 中心到角落的距离 ≈ 157.43 / 2 ≈ 78.7公里
    bounds_equator = (0.0, 0.0, 1.0, 1.0)
    expected_distance_equator = 78718  # 米，约78.7公里
    actual_distance_equator = calculate_max_distance_meters(bounds_equator)
    assert math.isclose(
        actual_distance_equator, expected_distance_equator, rel_tol=0.01
    )

    # 测试用例2: 柏林附近的小区域
    # 边界框: (52.5000, 13.4000, 52.5400, 13.4400)
    # 中心点: (52.5200, 13.4200)
    # 角落距离中心点约2603米
    bounds_berlin = (52.5000, 13.4000, 52.5400, 13.4400)
    expected_distance_berlin = 2603.4  # 米
    actual_distance_berlin = calculate_max_distance_meters(bounds_berlin)
    assert math.isclose(actual_distance_berlin, expected_distance_berlin, rel_tol=0.01)

    # 测试用例3: 更大的区域
    # 较大的区域应该产生更大的距离
    bounds_large = (52.0, 13.0, 53.0, 14.0)  # 约110km x 110km
    distance_large = calculate_max_distance_meters(bounds_large)
    distance_berlin = calculate_max_distance_meters(bounds_berlin)
    assert distance_large > distance_berlin


def test_generate_grid_points():
    """
    测试网格点生成算法。

    该函数在给定边界框内生成均匀分布的网格点，
    步长以米为单位，转换为度数时考虑纬度效应。
    """
    # 测试用例1: 200m x 200m 区域，100m 步长
    # 边界框: (52.5200, 13.4050, 52.5218, 13.4078)
    # 纬度范围: 0.0018度 ≈ 200米
    # 经度范围: 0.0028度 ≈ 200米 (在柏林纬度)
    # 使用100米步长，预期生成约6个点 (3纬度 x 2经度)
    bounds_small = (52.5200, 13.4050, 52.5218, 13.4078)
    step_meters = 100
    points = generate_grid_points(bounds_small, step_meters)

    # 由于地球曲率和坐标转换，实际点数可能略有差异
    # 基于算法计算，期望得到6个点
    expected_points = 6
    assert len(points) == expected_points

    # 验证第一个点接近边界框左下角
    first_point = points[0]
    assert math.isclose(first_point[0], 52.5200, abs_tol=0.001)
    assert math.isclose(first_point[1], 13.4050, abs_tol=0.001)

    # 验证所有点都有6位小数精度
    for point in points:
        assert len(str(point[0]).split(".")[-1]) <= 6
        assert len(str(point[1]).split(".")[-1]) <= 6

    # 测试用例2: 步长大于边界框
    # 当步长大于边界框时，应该只生成1-4个点（角落点）
    points_large_step = generate_grid_points(bounds_small, step_meters=500)
    assert 1 <= len(points_large_step) <= 4

    # 测试用例3: 验证点在边界框内
    for point in points:
        assert bounds_small[0] <= point[0] <= bounds_small[2]
        assert bounds_small[1] <= point[1] <= bounds_small[3]


def test_calculate_search_parameters():
    """
    测试动态搜索参数计算。

    该函数根据边界框大小和约束条件动态计算：
    - 初始搜索半径
    - 初始网格步长
    - 细化半径因子

    算法基于对角线距离的1/4作为初始半径。
    """
    # 测试用例1: 使用 Tiergarten 测试区域的实际边界
    # Tiergarten 边界: ~1.3km x 2.6km
    tiergarten_bounds = (52.5073, 13.3503, 52.5193, 13.3883)

    # 定义约束条件
    max_radius = 5000  # 最大允许半径5公里
    min_refinement_radius = 50  # 最小细化半径50米
    max_refinement_levels = 4  # 最大4层细化

    initial_radius, initial_grid_step, refinement_factors = calculate_search_parameters(
        tiergarten_bounds, max_radius, min_refinement_radius, max_refinement_levels
    )

    # 验证结果基于函数逻辑
    # 1. 半径应该是正数且在最大限制内
    assert 0 < initial_radius <= max_radius

    # 2. 网格步长应该是半径的0.8倍（算法设定）
    assert math.isclose(initial_grid_step, initial_radius * 0.8)

    # 3. 细化因子数量应该等于请求的层级数
    assert len(refinement_factors) == max_refinement_levels

    # 4. 细化因子应该都大于1.0，表示缩小
    assert all(factor > 1.0 for factor in refinement_factors)

    # 测试用例2: 非常小的区域
    # 当区域很小时，应该回退到最小细化半径的10倍
    small_bounds = (52.5200, 13.4050, 52.5210, 13.4060)  # 非常小的区域
    initial_radius_small, _, _ = calculate_search_parameters(
        small_bounds, max_radius, min_refinement_radius, max_refinement_levels
    )

    # 应该回退到最小值：50 * 10 = 500米
    expected_min_radius = min_refinement_radius * 10
    assert initial_radius_small == expected_min_radius

    # 测试用例3: 验证细化因子的几何级数特性
    # 细化因子应该形成几何级数，从初始半径平滑收敛到最小半径
    if initial_radius > min_refinement_radius and max_refinement_levels > 0:
        # 计算总的缩减比例
        total_reduction = initial_radius / min_refinement_radius

        # 验证几何级数：所有因子的乘积应该等于总缩减比例
        product_of_factors = 1.0
        for factor in refinement_factors:
            product_of_factors *= factor

        assert math.isclose(product_of_factors, total_reduction, rel_tol=0.01)
