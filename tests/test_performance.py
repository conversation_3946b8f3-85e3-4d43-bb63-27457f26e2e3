"""
性能基准测试模块

为 Google Maps Grid Search 项目提供性能基准测试功能。
测试新旧架构在相同任务下的性能表现对比。
"""

import time
import psutil
import os
import json
import subprocess
import argparse
from pathlib import Path
from typing import Dict, Any, List


class PerformanceBenchmark:
    """性能基准测试类"""

    def __init__(self, output_dir: str = "benchmark_results"):
        """初始化性能基准测试"""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.results = []

    def run_test(
        self, architecture: str, test_area: str, iterations: int = 3
    ) -> Dict[str, Any]:
        """
        运行单个性能测试

        Args:
            architecture: 架构类型 ("old" 或 "new")
            test_area: 测试区域
            iterations: 测试迭代次数

        Returns:
            测试结果字典
        """
        print(f"运行 {architecture} 架构在 {test_area} 区域的性能测试...")

        iteration_results = []

        for i in range(iterations):
            print(f"  迭代 {i+1}/{iterations}")

            # 记录初始状态
            start_time = time.time()
            process = psutil.Process(os.getpid())
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB

            # 执行测试
            try:
                if architecture == "new":
                    cmd = [
                        "python",
                        "-m",
                        "src.main",
                        "--test-area",
                        test_area,
                        "--dry-run",
                        "--mock-seed",
                        "42",
                        "--work-dir",
                        f"benchmark_{test_area}_{i}",
                    ]
                else:
                    cmd = [
                        "python",
                        "deprecation_src/grid_search.py",
                        "--test-area",
                        test_area,
                        "--dry-run",
                        "--mock-seed",
                        "42",
                    ]

                # 执行命令并捕获输出
                result = subprocess.run(
                    cmd, capture_output=True, text=True, timeout=300  # 5分钟超时
                )

                # 记录结束状态
                end_time = time.time()
                final_memory = process.memory_info().rss / 1024 / 1024  # MB

                # 计算指标
                elapsed_time = end_time - start_time
                peak_memory = max(initial_memory, final_memory)

                iteration_results.append(
                    {
                        "iteration": i + 1,
                        "elapsed_time": elapsed_time,
                        "peak_memory": peak_memory,
                        "exit_code": result.returncode,
                        "stdout_size": len(result.stdout),
                        "stderr_size": len(result.stderr),
                    }
                )

                if result.returncode != 0:
                    print(f"    警告: 测试返回非零退出码 {result.returncode}")
                    print(f"    stderr: {result.stderr[:200]}...")

            except subprocess.TimeoutExpired:
                print(f"    错误: 测试超时")
                iteration_results.append(
                    {
                        "iteration": i + 1,
                        "elapsed_time": 300,  # 超时时间
                        "peak_memory": -1,  # 未知
                        "exit_code": -1,  # 超时
                        "stdout_size": -1,
                        "stderr_size": -1,
                    }
                )
            except Exception as e:
                print(f"    错误: {e}")
                iteration_results.append(
                    {
                        "iteration": i + 1,
                        "elapsed_time": -1,  # 错误
                        "peak_memory": -1,  # 错误
                        "exit_code": -2,  # 异常
                        "stdout_size": -1,
                        "stderr_size": -1,
                    }
                )

        # 计算平均值（排除错误结果）
        valid_results = [r for r in iteration_results if r["elapsed_time"] > 0]
        if valid_results:
            avg_elapsed_time = sum(r["elapsed_time"] for r in valid_results) / len(
                valid_results
            )
            avg_peak_memory = sum(r["peak_memory"] for r in valid_results) / len(
                valid_results
            )
        else:
            avg_elapsed_time = -1
            avg_peak_memory = -1

        test_result = {
            "architecture": architecture,
            "test_area": test_area,
            "iterations": iteration_results,
            "avg_elapsed_time": avg_elapsed_time,
            "avg_peak_memory": avg_peak_memory,
            "successful_iterations": len(valid_results),
            "total_iterations": iterations,
        }

        self.results.append(test_result)
        return test_result

    def run_benchmark(self, test_areas: List[str] = None, iterations: int = 3):
        """
        运行完整的基准测试

        Args:
            test_areas: 要测试的区域列表
            iterations: 每个测试的迭代次数
        """
        if test_areas is None:
            test_areas = ["alexanderplatz", "tiergarten", "kreuzberg"]

        print("开始性能基准测试...")
        print(f"测试区域: {', '.join(test_areas)}")
        print(f"迭代次数: {iterations}")
        print("=" * 50)

        # 测试旧架构
        print("\n--- 测试旧架构 ---")
        for area in test_areas:
            self.run_test("old", area, iterations)

        # 测试新架构
        print("\n--- 测试新架构 ---")
        for area in test_areas:
            self.run_test("new", area, iterations)

        # 保存结果
        self.save_results()

        # 生成报告
        self.generate_report()

    def save_results(self):
        """保存测试结果"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        results_file = self.output_dir / f"benchmark_results_{timestamp}.json"

        with open(results_file, "w", encoding="utf-8") as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)

        print(f"\n测试结果已保存到: {results_file}")

    def generate_report(self):
        """生成性能对比报告"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        report_file = self.output_dir / f"benchmark_report_{timestamp}.md"

        # 按测试区域分组
        by_area = {}
        for result in self.results:
            area = result["test_area"]
            arch = result["architecture"]
            if area not in by_area:
                by_area[area] = {}
            by_area[area][arch] = result

        # 生成报告
        with open(report_file, "w", encoding="utf-8") as f:
            f.write("# 性能基准测试报告\n\n")
            f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("## 测试概述\n\n")
            f.write("- 测试目标: 对比新旧架构在相同任务下的性能表现\n")
            f.write("- 测试方法: 使用模拟数据(dry-run)执行相同测试区域\n")
            f.write("- 测试指标: 处理时间、内存使用\n\n")

            f.write("## 性能对比结果\n\n")

            for area, data in by_area.items():
                f.write(f"### 测试区域: {area}\n\n")

                old_data = data.get("old")
                new_data = data.get("new")

                if old_data and new_data:
                    # 时间对比
                    if (
                        old_data["avg_elapsed_time"] > 0
                        and new_data["avg_elapsed_time"] > 0
                    ):
                        time_improvement = (
                            (
                                old_data["avg_elapsed_time"]
                                - new_data["avg_elapsed_time"]
                            )
                            / old_data["avg_elapsed_time"]
                            * 100
                        )
                        f.write(
                            f"- **处理时间**: "
                            f"{old_data['avg_elapsed_time']:.2f}s → {new_data['avg_elapsed_time']:.2f}s "
                            f"({time_improvement:+.1f}%)\n"
                        )
                    else:
                        f.write("- **处理时间**: 数据不可用\n")

                    # 内存对比
                    if (
                        old_data["avg_peak_memory"] > 0
                        and new_data["avg_peak_memory"] > 0
                    ):
                        memory_improvement = (
                            (old_data["avg_peak_memory"] - new_data["avg_peak_memory"])
                            / old_data["avg_peak_memory"]
                            * 100
                        )
                        f.write(
                            f"- **内存使用**: "
                            f"{old_data['avg_peak_memory']:.1f}MB → {new_data['avg_peak_memory']:.1f}MB "
                            f"({memory_improvement:+.1f}%)\n"
                        )
                    else:
                        f.write("- **内存使用**: 数据不可用\n")

                    # 成功率对比
                    old_success = (
                        old_data["successful_iterations"]
                        / old_data["total_iterations"]
                        * 100
                    )
                    new_success = (
                        new_data["successful_iterations"]
                        / new_data["total_iterations"]
                        * 100
                    )
                    f.write(
                        f"- **成功率**: " f"{old_success:.1f}% → {new_success:.1f}%\n\n"
                    )
                else:
                    f.write("- 数据不完整\n\n")

            f.write("## 详细测试数据\n\n")
            f.write("```json\n")
            json.dump(self.results, f, indent=2, ensure_ascii=False)
            f.write("\n```\n")

        print(f"测试报告已生成: {report_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="性能基准测试")
    parser.add_argument(
        "--test-areas",
        nargs="+",
        default=["alexanderplatz", "tiergarten", "kreuzberg"],
        help="要测试的区域列表",
    )
    parser.add_argument("--iterations", type=int, default=3, help="每个测试的迭代次数")
    parser.add_argument("--output-dir", default="benchmark_results", help="输出目录")

    args = parser.parse_args()

    # 创建基准测试实例
    benchmark = PerformanceBenchmark(args.output_dir)

    # 运行基准测试
    benchmark.run_benchmark(args.test_areas, args.iterations)


if __name__ == "__main__":
    main()
