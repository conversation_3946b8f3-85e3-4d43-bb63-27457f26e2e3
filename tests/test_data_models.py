"""
数据模型单元测试

为 Google Maps Grid Search 项目的数据模型提供全面的单元测试。
测试覆盖序列化、状态转换、生命周期管理等核心功能。

基于 TECHNICAL_DESIGN.md 中的测试策略设计。
"""

import unittest
from datetime import datetime

from src.data_models import (
    SearchStatus,
    GridCell,
    SearchOrchestration,
    create_root_cell,
    create_search_orchestration,
)


class TestSearchStatus(unittest.TestCase):
    """测试 SearchStatus 枚举"""

    def test_enum_values(self):
        """测试枚举值是否正确"""
        self.assertEqual(SearchStatus.PENDING.value, "pending")
        self.assertEqual(SearchStatus.PROCESSING.value, "processing")
        self.assertEqual(SearchStatus.SEARCH_COMPLETE.value, "search_complete")
        self.assertEqual(SearchStatus.REFINEMENT_NEEDED.value, "refinement_needed")
        self.assertEqual(SearchStatus.REFINEMENT_COMPLETE.value, "refinement_complete")
        self.assertEqual(SearchStatus.FAILED.value, "failed")

    def test_enum_uniqueness(self):
        """测试枚举值的唯一性"""
        values = [status.value for status in SearchStatus]
        self.assertEqual(len(values), len(set(values)), "枚举值应该唯一")


class TestGridCell(unittest.TestCase):
    """测试 GridCell 数据模型"""

    def setUp(self):
        """测试前准备"""
        self.test_cell = GridCell(
            cell_id="L0-52.520000-13.405000",
            status=SearchStatus.PENDING,
            layer_id=0,
            center_lat=52.52,
            center_lng=13.405,
            search_radius=1000.0,
        )

    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.test_cell.cell_id, "L0-52.520000-13.405000")
        self.assertEqual(self.test_cell.status, SearchStatus.PENDING)
        self.assertEqual(self.test_cell.layer_id, 0)
        self.assertEqual(self.test_cell.center_lat, 52.52)
        self.assertEqual(self.test_cell.center_lng, 13.405)
        self.assertEqual(self.test_cell.search_radius, 1000.0)
        self.assertEqual(self.test_cell.parent_id, None)
        self.assertEqual(self.test_cell.children_ids, [])
        self.assertEqual(self.test_cell.results_count, 0)
        self.assertEqual(self.test_cell.place_ids, set())
        self.assertEqual(self.test_cell.api_calls_count, 0)
        self.assertIsNone(self.test_cell.error_message)
        self.assertEqual(self.test_cell.metadata, {})

    def test_auto_cell_id_generation(self):
        """测试自动生成 cell_id"""
        cell = GridCell(
            cell_id="",  # 空字符串触发自动生成
            status=SearchStatus.PENDING,
            layer_id=1,
            center_lat=52.52,
            center_lng=13.405,
            search_radius=500.0,
        )
        self.assertEqual(cell.cell_id, "L1-52.520000-13.405000")

    def test_place_ids_as_set(self):
        """测试 place_ids 被正确转换为集合"""
        # 测试从列表转换
        cell = GridCell(
            cell_id="L0-52.520000-13.405000",
            status=SearchStatus.PENDING,
            layer_id=0,
            center_lat=52.52,
            center_lng=13.405,
            search_radius=1000.0,
            place_ids=["place1", "place2", "place1"],  # 包含重复项
        )
        self.assertIsInstance(cell.place_ids, set)
        self.assertEqual(len(cell.place_ids), 2)  # 重复项被去除
        self.assertIn("place1", cell.place_ids)
        self.assertIn("place2", cell.place_ids)

    def test_update_status(self):
        """测试状态更新"""
        old_updated_at = self.test_cell.updated_at
        self.test_cell.update_status(SearchStatus.PROCESSING)
        self.assertEqual(self.test_cell.status, SearchStatus.PROCESSING)
        self.assertGreater(self.test_cell.updated_at, old_updated_at)

    def test_record_search_results(self):
        """测试记录搜索结果"""
        old_updated_at = self.test_cell.updated_at
        place_ids = {"place1", "place2", "place3"}
        self.test_cell.record_search_results(3, place_ids, 2)
        self.assertEqual(self.test_cell.results_count, 3)
        self.assertEqual(self.test_cell.place_ids, place_ids)
        self.assertEqual(self.test_cell.api_calls_count, 2)
        self.assertGreater(self.test_cell.updated_at, old_updated_at)

    def test_add_child(self):
        """测试添加子节点"""
        old_updated_at = self.test_cell.updated_at
        self.test_cell.add_child("L1-52.520000-13.405000")
        self.assertIn("L1-52.520000-13.405000", self.test_cell.children_ids)
        self.assertGreater(self.test_cell.updated_at, old_updated_at)

        # 测试重复添加
        original_length = len(self.test_cell.children_ids)
        self.test_cell.add_child("L1-52.520000-13.405000")
        self.assertEqual(len(self.test_cell.children_ids), original_length)

    def test_set_error(self):
        """测试设置错误状态"""
        old_updated_at = self.test_cell.updated_at
        error_msg = "API调用失败"
        self.test_cell.set_error(error_msg)
        self.assertEqual(self.test_cell.error_message, error_msg)
        self.assertEqual(self.test_cell.status, SearchStatus.FAILED)
        self.assertGreater(self.test_cell.updated_at, old_updated_at)

    def test_needs_refinement(self):
        """测试是否需要细化判断"""
        self.test_cell.status = SearchStatus.REFINEMENT_NEEDED
        self.assertTrue(self.test_cell.needs_refinement())

        self.test_cell.status = SearchStatus.PENDING
        self.assertFalse(self.test_cell.needs_refinement())

    def test_is_complete(self):
        """测试是否完成判断"""
        # 测试搜索完成状态
        self.test_cell.status = SearchStatus.SEARCH_COMPLETE
        self.assertTrue(self.test_cell.is_complete())

        # 测试细化完成状态
        self.test_cell.status = SearchStatus.REFINEMENT_COMPLETE
        self.assertTrue(self.test_cell.is_complete())

        # 测试其他状态
        self.test_cell.status = SearchStatus.PENDING
        self.assertFalse(self.test_cell.is_complete())

    def test_is_leaf(self):
        """测试是否为叶子节点判断"""
        self.assertTrue(self.test_cell.is_leaf())

        self.test_cell.add_child("L1-child")
        self.assertFalse(self.test_cell.is_leaf())

    def test_get_summary(self):
        """测试获取摘要信息"""
        summary = self.test_cell.get_summary()
        self.assertEqual(summary["cell_id"], "L0-52.520000-13.405000")
        self.assertEqual(summary["status"], "pending")
        self.assertEqual(summary["layer_id"], 0)
        self.assertEqual(summary["center"], {"lat": 52.52, "lng": 13.405})
        self.assertEqual(summary["results_count"], 0)
        self.assertEqual(summary["api_calls_count"], 0)
        self.assertFalse(summary["has_children"])
        self.assertFalse(summary["error"])

    def test_to_dict(self):
        """测试转换为字典"""
        place_ids = {"place1", "place2"}
        self.test_cell.record_search_results(2, place_ids)
        self.test_cell.add_child("L1-child1")
        self.test_cell.add_child("L1-child2")

        data = self.test_cell.to_dict()

        # 检查基本字段
        self.assertEqual(data["cell_id"], "L0-52.520000-13.405000")
        self.assertEqual(data["status"], "pending")
        self.assertEqual(data["layer_id"], 0)
        self.assertEqual(data["center_lat"], 52.52)
        self.assertEqual(data["center_lng"], 13.405)
        self.assertEqual(data["search_radius"], 1000.0)
        self.assertEqual(data["results_count"], 2)
        self.assertEqual(set(data["place_ids"]), place_ids)
        self.assertEqual(data["api_calls_count"], 1)
        self.assertEqual(data["children_ids"], ["L1-child1", "L1-child2"])
        self.assertIsNone(data["error_message"])

        # 检查时间戳格式
        self.assertIsInstance(data["created_at"], str)
        self.assertIsInstance(data["updated_at"], str)
        # 验证可以解析时间戳
        datetime.fromisoformat(data["created_at"])
        datetime.fromisoformat(data["updated_at"])

    def test_from_dict(self):
        """测试从字典创建"""
        # 创建测试数据
        test_data = {
            "cell_id": "L0-52.520000-13.405000",
            "status": "pending",
            "layer_id": 0,
            "center_lat": 52.52,
            "center_lng": 13.405,
            "search_radius": 1000.0,
            "parent_id": None,
            "children_ids": ["L1-child1", "L1-child2"],
            "results_count": 5,
            "place_ids": ["place1", "place2"],  # 列表格式
            "api_calls_count": 3,
            "created_at": "2023-01-01T12:00:00",
            "updated_at": "2023-01-01T12:05:00",
            "error_message": None,
            "metadata": {},
        }

        cell = GridCell.from_dict(test_data)

        # 验证字段
        self.assertEqual(cell.cell_id, "L0-52.520000-13.405000")
        self.assertEqual(cell.status, SearchStatus.PENDING)
        self.assertEqual(cell.layer_id, 0)
        self.assertEqual(cell.center_lat, 52.52)
        self.assertEqual(cell.center_lng, 13.405)
        self.assertEqual(cell.search_radius, 1000.0)
        self.assertEqual(cell.parent_id, None)
        self.assertEqual(cell.children_ids, ["L1-child1", "L1-child2"])
        self.assertEqual(cell.results_count, 5)
        self.assertEqual(cell.place_ids, {"place1", "place2"})  # 转换为集合
        self.assertEqual(cell.api_calls_count, 3)
        self.assertEqual(cell.error_message, None)
        self.assertEqual(cell.metadata, {})

        # 验证时间戳
        self.assertEqual(cell.created_at, datetime.fromisoformat("2023-01-01T12:00:00"))
        self.assertEqual(cell.updated_at, datetime.fromisoformat("2023-01-01T12:05:00"))

    def test_serialization_roundtrip(self):
        """测试序列化和反序列化的往返一致性"""
        # 设置测试数据
        place_ids = {"place1", "place2", "place3"}
        self.test_cell.record_search_results(3, place_ids, 2)
        self.test_cell.add_child("L1-child1")
        self.test_cell.add_child("L1-child2")
        self.test_cell.metadata = {"test_key": "test_value"}

        # 序列化
        data = self.test_cell.to_dict()

        # 反序列化
        restored_cell = GridCell.from_dict(data)

        # 验证一致性
        self.assertEqual(restored_cell.cell_id, self.test_cell.cell_id)
        self.assertEqual(restored_cell.status, self.test_cell.status)
        self.assertEqual(restored_cell.layer_id, self.test_cell.layer_id)
        self.assertEqual(restored_cell.center_lat, self.test_cell.center_lat)
        self.assertEqual(restored_cell.center_lng, self.test_cell.center_lng)
        self.assertEqual(restored_cell.search_radius, self.test_cell.search_radius)
        self.assertEqual(restored_cell.parent_id, self.test_cell.parent_id)
        self.assertEqual(restored_cell.children_ids, self.test_cell.children_ids)
        self.assertEqual(restored_cell.results_count, self.test_cell.results_count)
        self.assertEqual(restored_cell.place_ids, self.test_cell.place_ids)
        self.assertEqual(restored_cell.api_calls_count, self.test_cell.api_calls_count)
        self.assertEqual(restored_cell.error_message, self.test_cell.error_message)
        self.assertEqual(restored_cell.metadata, self.test_cell.metadata)


class TestSearchOrchestration(unittest.TestCase):
    """测试 SearchOrchestration 数据模型"""

    def setUp(self):
        """测试前准备"""
        self.test_orchestration = SearchOrchestration(
            task_id="test-task-123", place_type="restaurant", location="Berlin, Germany"
        )

        # 添加一些测试单元
        self.root_cell = GridCell(
            cell_id="L0-52.520000-13.405000",
            status=SearchStatus.PENDING,
            layer_id=0,
            center_lat=52.52,
            center_lng=13.405,
            search_radius=1000.0,
        )
        self.test_orchestration.add_cell(self.root_cell)

        self.child_cell1 = GridCell(
            cell_id="L1-52.521000-13.406000",
            status=SearchStatus.PENDING,
            layer_id=1,
            center_lat=52.521,
            center_lng=13.406,
            search_radius=500.0,
            parent_id="L0-52.520000-13.405000",
        )
        self.test_orchestration.add_cell(self.child_cell1)

        self.child_cell2 = GridCell(
            cell_id="L1-52.519000-13.404000",
            status=SearchStatus.PENDING,
            layer_id=1,
            center_lat=52.519,
            center_lng=13.404,
            search_radius=500.0,
            parent_id="L0-52.520000-13.405000",
        )
        self.test_orchestration.add_cell(self.child_cell2)

    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.test_orchestration.task_id, "test-task-123")
        self.assertEqual(self.test_orchestration.place_type, "restaurant")
        self.assertEqual(self.test_orchestration.location, "Berlin, Germany")
        self.assertEqual(self.test_orchestration.current_layer, 0)
        self.assertEqual(self.test_orchestration.completed_layers, [])
        self.assertIsInstance(self.test_orchestration.cells, dict)
        self.assertIsInstance(self.test_orchestration.layers, dict)
        self.assertIsInstance(self.test_orchestration.config, dict)
        self.assertIsInstance(self.test_orchestration.metrics, dict)

    def test_auto_task_id_generation(self):
        """测试自动生成任务ID"""
        orchestration = SearchOrchestration(
            task_id="",  # 空字符串触发自动生成
            place_type="cafe",
            location="Hamburg, Germany",
        )
        self.assertTrue(orchestration.task_id.startswith("cafe-Hamburg, Germany-"))

    def test_add_cell(self):
        """测试添加网格单元"""
        # 验证单元已添加
        self.assertIn("L0-52.520000-13.405000", self.test_orchestration.cells)
        self.assertIn("L1-52.521000-13.406000", self.test_orchestration.cells)
        self.assertIn("L1-52.519000-13.404000", self.test_orchestration.cells)

        # 验证层级索引
        self.assertIn("L0-52.520000-13.405000", self.test_orchestration.layers[0])
        self.assertIn("L1-52.521000-13.406000", self.test_orchestration.layers[1])
        self.assertIn("L1-52.519000-13.404000", self.test_orchestration.layers[1])

        # 验证指标更新
        self.assertEqual(self.test_orchestration.metrics["total_cells_created"], 3)

    def test_get_cell(self):
        """测试获取网格单元"""
        cell = self.test_orchestration.get_cell("L0-52.520000-13.405000")
        self.assertIsNotNone(cell)
        self.assertEqual(cell.cell_id, "L0-52.520000-13.405000")

        # 测试获取不存在的单元
        cell = self.test_orchestration.get_cell("non-existent")
        self.assertIsNone(cell)

    def test_get_layer_cells(self):
        """测试获取层级网格单元"""
        layer0_cells = self.test_orchestration.get_layer_cells(0)
        layer1_cells = self.test_orchestration.get_layer_cells(1)

        self.assertEqual(len(layer0_cells), 1)
        self.assertEqual(layer0_cells[0].cell_id, "L0-52.520000-13.405000")

        self.assertEqual(len(layer1_cells), 2)
        cell_ids = {cell.cell_id for cell in layer1_cells}
        self.assertIn("L1-52.521000-13.406000", cell_ids)
        self.assertIn("L1-52.519000-13.404000", cell_ids)

    def test_get_pending_cells(self):
        """测试获取待处理网格单元"""
        # 将一个单元标记为处理中
        cell = self.test_orchestration.get_cell("L1-52.521000-13.406000")
        cell.update_status(SearchStatus.PROCESSING)

        # 测试默认层级（current_layer = 0）
        pending_cells = self.test_orchestration.get_pending_cells()
        self.assertEqual(len(pending_cells), 1)
        self.assertEqual(pending_cells[0].cell_id, "L0-52.520000-13.405000")

        # 测试指定层级1
        pending_cells_layer1 = self.test_orchestration.get_pending_cells(layer_id=1)
        self.assertEqual(len(pending_cells_layer1), 1)
        self.assertEqual(pending_cells_layer1[0].cell_id, "L1-52.519000-13.404000")

    def test_get_processing_cells(self):
        """测试获取处理中网格单元"""
        # 将一个单元标记为处理中
        cell = self.test_orchestration.get_cell("L1-52.521000-13.406000")
        cell.update_status(SearchStatus.PROCESSING)

        # 测试默认层级（current_layer = 0）
        processing_cells = self.test_orchestration.get_processing_cells()
        self.assertEqual(len(processing_cells), 0)

        # 测试指定层级1
        processing_cells_layer1 = self.test_orchestration.get_processing_cells(
            layer_id=1
        )
        self.assertEqual(len(processing_cells_layer1), 1)
        self.assertEqual(processing_cells_layer1[0].cell_id, "L1-52.521000-13.406000")

    def test_get_cells_needing_refinement(self):
        """测试获取需要细化的网格单元"""
        # 将一个单元标记为需要细化
        self.child_cell1.update_status(SearchStatus.REFINEMENT_NEEDED)

        refinement_cells = self.test_orchestration.get_cells_needing_refinement()
        self.assertEqual(len(refinement_cells), 1)
        self.assertEqual(refinement_cells[0].cell_id, "L1-52.521000-13.406000")

    def test_mark_layer_complete(self):
        """测试标记层级完成"""
        self.test_orchestration.mark_layer_complete(0)
        self.assertIn(0, self.test_orchestration.completed_layers)
        self.assertEqual(self.test_orchestration.metrics["layers_completed"], 1)

        # 测试重复标记
        original_length = len(self.test_orchestration.completed_layers)
        self.test_orchestration.mark_layer_complete(0)
        self.assertEqual(len(self.test_orchestration.completed_layers), original_length)

    def test_update_metrics(self):
        """测试更新指标"""
        # 设置一些测试数据
        self.root_cell.update_status(SearchStatus.SEARCH_COMPLETE)
        self.root_cell.record_search_results(10, {"place1", "place2"}, 2)
        self.child_cell1.update_status(SearchStatus.FAILED)
        self.child_cell2.update_status(SearchStatus.REFINEMENT_COMPLETE)
        self.child_cell2.record_search_results(5, {"place3", "place4"}, 1)

        # 更新指标
        self.test_orchestration.update_metrics(processing_time_seconds=120)

        # 验证指标
        self.assertEqual(self.test_orchestration.metrics["total_cells_processed"], 2)
        self.assertEqual(self.test_orchestration.metrics["failed_cells"], 1)
        self.assertEqual(self.test_orchestration.metrics["total_places_found"], 4)
        self.assertEqual(self.test_orchestration.metrics["total_api_calls"], 3)
        self.assertEqual(
            self.test_orchestration.metrics["processing_time_seconds"], 120
        )

    def test_get_all_place_ids(self):
        """测试获取所有地点ID"""
        # 设置测试数据
        self.root_cell.record_search_results(2, {"place1", "place2"})
        self.child_cell1.record_search_results(2, {"place2", "place3"})  # 包含重复
        self.child_cell2.record_search_results(1, {"place4"})

        all_place_ids = self.test_orchestration.get_all_place_ids()
        self.assertEqual(len(all_place_ids), 4)
        expected_ids = {"place1", "place2", "place3", "place4"}
        self.assertEqual(all_place_ids, expected_ids)

    def test_get_summary(self):
        """测试获取摘要信息"""
        summary = self.test_orchestration.get_summary()
        self.assertEqual(summary["task_id"], "test-task-123")
        self.assertEqual(summary["place_type"], "restaurant")
        self.assertEqual(summary["location"], "Berlin, Germany")
        self.assertEqual(summary["current_layer"], 0)
        self.assertEqual(summary["completed_layers"], 0)
        self.assertEqual(summary["total_layers"], 2)
        self.assertEqual(summary["total_cells"], 3)
        self.assertIsInstance(summary["metrics"], dict)
        self.assertIn("created_at", summary)
        self.assertIn("updated_at", summary)

    def test_get_overall_status(self):
        """测试获取整体状态"""
        # 测试初始化状态
        empty_orch = SearchOrchestration("empty-task", "cafe", "Hamburg")
        self.assertEqual(empty_orch.get_overall_status(), "initialized")

        # 测试待处理状态
        self.assertEqual(self.test_orchestration.get_overall_status(), "pending")

        # 测试进行中状态
        self.root_cell.update_status(SearchStatus.SEARCH_COMPLETE)
        self.assertEqual(self.test_orchestration.get_overall_status(), "in_progress")

        # 测试完成状态
        self.child_cell1.update_status(SearchStatus.SEARCH_COMPLETE)
        self.child_cell2.update_status(SearchStatus.SEARCH_COMPLETE)
        self.assertEqual(self.test_orchestration.get_overall_status(), "completed")

        # 测试失败状态
        failed_orch = SearchOrchestration("failed-task", "bar", "Munich")
        failed_cell1 = GridCell(
            "L0-fail1", SearchStatus.FAILED, 0, 52.52, 13.405, 1000.0
        )
        failed_cell2 = GridCell(
            "L0-fail2", SearchStatus.FAILED, 0, 52.521, 13.406, 1000.0
        )
        failed_orch.add_cell(failed_cell1)
        failed_orch.add_cell(failed_cell2)
        self.assertEqual(failed_orch.get_overall_status(), "failed")

    def test_is_complete(self):
        """测试是否完成"""
        self.assertFalse(self.test_orchestration.is_complete())

        # 将所有单元标记为完成
        for cell in self.test_orchestration.cells.values():
            cell.update_status(SearchStatus.SEARCH_COMPLETE)

        self.assertTrue(self.test_orchestration.is_complete())

    def test_has_pending_work(self):
        """测试是否有待处理工作"""
        self.assertTrue(self.test_orchestration.has_pending_work())

        # 将所有单元标记为完成
        for cell in self.test_orchestration.cells.values():
            cell.update_status(SearchStatus.SEARCH_COMPLETE)

        self.assertFalse(self.test_orchestration.has_pending_work())

    def test_to_dict(self):
        """测试转换为字典"""
        # 设置一些测试数据
        self.root_cell.record_search_results(5, {"place1", "place2"})
        self.child_cell1.update_status(SearchStatus.PROCESSING)
        self.test_orchestration.mark_layer_complete(0)
        self.test_orchestration.update_metrics(processing_time_seconds=60)

        data = self.test_orchestration.to_dict()

        # 验证基本字段
        self.assertEqual(data["task_id"], "test-task-123")
        self.assertEqual(data["place_type"], "restaurant")
        self.assertEqual(data["location"], "Berlin, Germany")
        self.assertEqual(data["current_layer"], 0)
        self.assertEqual(data["completed_layers"], [0])

        # 验证cells
        self.assertIn("L0-52.520000-13.405000", data["cells"])
        self.assertIn("L1-52.521000-13.406000", data["cells"])
        self.assertIn("L1-52.519000-13.404000", data["cells"])

        # 验证layers
        self.assertIn(0, data["layers"])
        self.assertIn(1, data["layers"])
        self.assertIn("L0-52.520000-13.405000", data["layers"][0])

        # 验证配置和指标
        self.assertIsInstance(data["config"], dict)
        self.assertIsInstance(data["metrics"], dict)
        self.assertEqual(data["metrics"]["processing_time_seconds"], 60)

        # 验证时间戳
        self.assertIsInstance(data["created_at"], str)
        self.assertIsInstance(data["updated_at"], str)

    def test_from_dict(self):
        """测试从字典创建"""
        # 创建测试数据
        test_data = {
            "task_id": "test-from-dict",
            "place_type": "hotel",
            "location": "Munich, Germany",
            "cells": {
                "L0-48.1351-11.5820": {
                    "cell_id": "L0-48.1351-11.5820",
                    "status": "pending",
                    "layer_id": 0,
                    "center_lat": 48.1351,
                    "center_lng": 11.5820,
                    "search_radius": 1000.0,
                    "parent_id": None,
                    "children_ids": [],
                    "results_count": 0,
                    "place_ids": [],
                    "api_calls_count": 0,
                    "created_at": "2023-01-01T12:00:00",
                    "updated_at": "2023-01-01T12:00:00",
                    "error_message": None,
                    "metadata": {},
                }
            },
            "layers": {0: ["L0-48.1351-11.5820"]},
            "config": {"max_refinement_levels": 4, "initial_radius": 1000},
            "metrics": {"total_api_calls": 0, "total_places_found": 0},
            "created_at": "2023-01-01T12:00:00",
            "updated_at": "2023-01-01T12:00:00",
            "current_layer": 0,
            "completed_layers": [],
        }

        orchestration = SearchOrchestration.from_dict(test_data)

        # 验证字段
        self.assertEqual(orchestration.task_id, "test-from-dict")
        self.assertEqual(orchestration.place_type, "hotel")
        self.assertEqual(orchestration.location, "Munich, Germany")
        self.assertEqual(orchestration.current_layer, 0)
        self.assertEqual(orchestration.completed_layers, [])

        # 验证cells
        self.assertIn("L0-48.1351-11.5820", orchestration.cells)
        cell = orchestration.cells["L0-48.1351-11.5820"]
        self.assertEqual(cell.cell_id, "L0-48.1351-11.5820")
        self.assertEqual(cell.status, SearchStatus.PENDING)
        self.assertEqual(cell.layer_id, 0)
        self.assertEqual(cell.center_lat, 48.1351)
        self.assertEqual(cell.center_lng, 11.5820)

        # 验证layers
        self.assertIn(0, orchestration.layers)
        self.assertIn("L0-48.1351-11.5820", orchestration.layers[0])

        # 验证配置和指标
        self.assertEqual(orchestration.config["max_refinement_levels"], 4)
        self.assertEqual(orchestration.config["initial_radius"], 1000)
        self.assertEqual(orchestration.metrics["total_api_calls"], 0)

        # 验证时间戳
        self.assertEqual(
            orchestration.created_at, datetime.fromisoformat("2023-01-01T12:00:00")
        )
        self.assertEqual(
            orchestration.updated_at, datetime.fromisoformat("2023-01-01T12:00:00")
        )

    def test_serialization_roundtrip(self):
        """测试序列化和反序列化的往返一致性"""
        # 设置测试数据
        self.root_cell.record_search_results(3, {"place1", "place2"}, 1)
        self.child_cell1.update_status(SearchStatus.PROCESSING)
        self.child_cell2.update_status(SearchStatus.REFINEMENT_NEEDED)
        self.test_orchestration.mark_layer_complete(0)
        self.test_orchestration.config["test_param"] = "test_value"
        self.test_orchestration.update_metrics(processing_time_seconds=180)

        # 序列化
        data = self.test_orchestration.to_dict()

        # 反序列化
        restored_orch = SearchOrchestration.from_dict(data)

        # 验证一致性
        self.assertEqual(restored_orch.task_id, self.test_orchestration.task_id)
        self.assertEqual(restored_orch.place_type, self.test_orchestration.place_type)
        self.assertEqual(restored_orch.location, self.test_orchestration.location)
        self.assertEqual(
            restored_orch.current_layer, self.test_orchestration.current_layer
        )
        self.assertEqual(
            restored_orch.completed_layers, self.test_orchestration.completed_layers
        )
        self.assertEqual(restored_orch.config, self.test_orchestration.config)
        self.assertEqual(restored_orch.metrics, self.test_orchestration.metrics)

        # 验证cells一致性
        self.assertEqual(len(restored_orch.cells), len(self.test_orchestration.cells))
        for cell_id, original_cell in self.test_orchestration.cells.items():
            self.assertIn(cell_id, restored_orch.cells)
            restored_cell = restored_orch.cells[cell_id]
            self.assertEqual(restored_cell.cell_id, original_cell.cell_id)
            self.assertEqual(restored_cell.status, original_cell.status)
            self.assertEqual(restored_cell.layer_id, original_cell.layer_id)
            self.assertEqual(restored_cell.results_count, original_cell.results_count)
            self.assertEqual(restored_cell.place_ids, original_cell.place_ids)
            self.assertEqual(
                restored_cell.api_calls_count, original_cell.api_calls_count
            )

    def test_save_and_load_file(self):
        """测试保存和加载文件功能"""
        # 这个测试需要实际文件I/O，我们在集成测试中会更全面地测试
        pass


class TestFactoryFunctions(unittest.TestCase):
    """测试工厂函数"""

    def test_create_root_cell(self):
        """测试创建根网格单元"""
        cell = create_root_cell(52.52, 13.405, 1000.0, "restaurant")
        self.assertEqual(cell.cell_id, "L0-52.520000-13.405000")
        self.assertEqual(cell.status, SearchStatus.PENDING)
        self.assertEqual(cell.layer_id, 0)
        self.assertEqual(cell.center_lat, 52.52)
        self.assertEqual(cell.center_lng, 13.405)
        self.assertEqual(cell.search_radius, 1000.0)
        self.assertEqual(cell.metadata["place_type"], "restaurant")

    def test_create_search_orchestration(self):
        """测试创建搜索编排对象"""
        orchestration = create_search_orchestration(
            "cafe", "Hamburg, Germany", 53.5511, 9.9937, 800.0
        )
        self.assertTrue(orchestration.task_id.startswith("cafe-Hamburg, Germany-"))
        self.assertEqual(orchestration.place_type, "cafe")
        self.assertEqual(orchestration.location, "Hamburg, Germany")
        self.assertEqual(len(orchestration.cells), 1)
        self.assertIn(0, orchestration.layers)

        # 验证根单元
        root_cell_id = orchestration.layers[0][0]
        root_cell = orchestration.cells[root_cell_id]
        self.assertEqual(root_cell.cell_id, "L0-53.551100-9.993700")
        self.assertEqual(root_cell.status, SearchStatus.PENDING)
        self.assertEqual(root_cell.layer_id, 0)
        self.assertEqual(root_cell.center_lat, 53.5511)
        self.assertEqual(root_cell.center_lng, 9.9937)
        self.assertEqual(root_cell.search_radius, 800.0)


if __name__ == "__main__":
    unittest.main()
