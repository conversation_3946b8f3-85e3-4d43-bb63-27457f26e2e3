"""
状态管理器单元测试

为 Google Maps Grid Search 项目的状态管理器提供全面的单元测试。
测试覆盖原子化保存、加载、备份等核心功能。

基于 TECHNICAL_DESIGN.md 中的测试策略设计。
"""

import json
import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch

from src.state_manager import StateManager, create_state_manager
from src.data_models import SearchOrchestration, GridCell, SearchStatus


class TestStateManager(unittest.TestCase):
    """测试 StateManager 类"""

    def setUp(self):
        """测试前准备"""
        # 创建临时工作目录
        self.temp_dir = Path(tempfile.mkdtemp())
        self.state_file = "test_orchestration.json"

        # 创建状态管理器
        self.state_manager = StateManager(
            work_dir=str(self.temp_dir), state_file=self.state_file, backup_enabled=True
        )

        # 创建测试编排对象
        self.test_orchestration = SearchOrchestration(
            task_id="test-task-123", place_type="restaurant", location="Berlin, Germany"
        )

        # 添加一些测试单元
        root_cell = GridCell(
            cell_id="L0-52.520000-13.405000",
            status=SearchStatus.PENDING,
            layer_id=0,
            center_lat=52.52,
            center_lng=13.405,
            search_radius=1000.0,
        )
        self.test_orchestration.add_cell(root_cell)

    def tearDown(self):
        """测试后清理"""
        # 清理临时目录
        import shutil

        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)

    def test_initialization(self):
        """测试初始化"""
        # 测试默认参数
        default_manager = StateManager()
        self.assertEqual(default_manager.work_dir, Path("."))
        self.assertEqual(default_manager.state_file, "orchestration.json")
        self.assertTrue(default_manager.backup_enabled)

        # 测试自定义参数
        self.assertEqual(self.state_manager.work_dir, self.temp_dir)
        self.assertEqual(self.state_manager.state_file, self.state_file)
        self.assertTrue(self.state_manager.backup_enabled)

    def test_ensure_work_dir_exists(self):
        """测试确保工作目录存在"""
        # 创建嵌套目录路径
        nested_dir = self.temp_dir / "nested" / "deep" / "path"
        manager = StateManager(work_dir=str(nested_dir))

        # 目录应该自动创建
        self.assertTrue(nested_dir.exists())

    def test_get_file_paths(self):
        """测试获取文件路径"""
        state_path = self.state_manager.get_state_file_path()
        backup_path = self.state_manager.get_backup_file_path()
        temp_path = self.state_manager.get_temp_file_path()

        self.assertEqual(state_path, self.temp_dir / self.state_file)
        self.assertEqual(backup_path, self.temp_dir / f"{self.state_file}.backup")
        self.assertEqual(temp_path, self.temp_dir / f"{self.state_file}.tmp")

    def test_save_state(self):
        """测试保存状态"""
        # 保存状态
        result = self.state_manager.save_state(self.test_orchestration)
        self.assertTrue(result)

        # 验证文件已创建
        state_file_path = self.state_manager.get_state_file_path()
        self.assertTrue(state_file_path.exists())

        # 验证文件内容
        with open(state_file_path, "r", encoding="utf-8") as f:
            data = json.load(f)

        self.assertEqual(data["task_id"], "test-task-123")
        self.assertEqual(data["place_type"], "restaurant")
        self.assertIn("L0-52.520000-13.405000", data["cells"])

    def test_save_state_atomicity(self):
        """测试保存状态的原子性"""
        # 保存初始状态
        self.state_manager.save_state(self.test_orchestration)

        # 修改编排对象
        self.test_orchestration.location = "Hamburg, Germany"

        # 保存修改后的状态
        result = self.state_manager.save_state(self.test_orchestration)
        self.assertTrue(result)

        # 验证文件内容已更新
        state_file_path = self.state_manager.get_state_file_path()
        with open(state_file_path, "r", encoding="utf-8") as f:
            data = json.load(f)

        self.assertEqual(data["location"], "Hamburg, Germany")

    def test_save_state_with_backup(self):
        """测试保存状态时创建备份"""
        # 保存初始状态
        self.state_manager.save_state(self.test_orchestration)

        # 验证备份文件不存在（首次保存）
        backup_file_path = self.state_manager.get_backup_file_path()
        self.assertFalse(backup_file_path.exists())

        # 修改编排对象并再次保存
        self.test_orchestration.location = "Hamburg, Germany"
        result = self.state_manager.save_state(self.test_orchestration)
        self.assertTrue(result)

        # 验证备份文件已创建
        self.assertTrue(backup_file_path.exists())

        # 验证备份文件内容是旧状态
        with open(backup_file_path, "r", encoding="utf-8") as f:
            backup_data = json.load(f)

        self.assertEqual(backup_data["location"], "Berlin, Germany")

    def test_load_state(self):
        """测试加载状态"""
        # 先保存状态
        self.state_manager.save_state(self.test_orchestration)

        # 加载状态
        loaded_orchestration = self.state_manager.load_state()

        # 验证加载的对象
        self.assertIsNotNone(loaded_orchestration)
        self.assertEqual(loaded_orchestration.task_id, "test-task-123")
        self.assertEqual(loaded_orchestration.place_type, "restaurant")
        self.assertEqual(loaded_orchestration.location, "Berlin, Germany")
        self.assertIn("L0-52.520000-13.405000", loaded_orchestration.cells)

    def test_load_state_file_not_exists(self):
        """测试加载不存在的状态文件"""
        # 确保文件不存在
        state_file_path = self.state_manager.get_state_file_path()
        if state_file_path.exists():
            state_file_path.unlink()

        # 加载状态应该返回 None
        loaded_orchestration = self.state_manager.load_state()
        self.assertIsNone(loaded_orchestration)

    def test_load_state_with_fallback_no_backup(self):
        """测试带回退的加载（无备份）"""
        # 先保存状态
        self.state_manager.save_state(self.test_orchestration)

        # 加载状态
        loaded_orchestration = self.state_manager.load_state_with_fallback()

        # 验证加载的对象
        self.assertIsNotNone(loaded_orchestration)
        self.assertEqual(loaded_orchestration.task_id, "test-task-123")

    def test_load_state_with_fallback_with_backup(self):
        """测试带回退的加载（有备份）"""
        # 保存初始状态
        self.state_manager.save_state(self.test_orchestration)

        # 创建损坏的主状态文件
        state_file_path = self.state_manager.get_state_file_path()
        with open(state_file_path, "w") as f:
            f.write("invalid json content")

        # 创建备份文件
        backup_file_path = self.state_manager.get_backup_file_path()
        with open(backup_file_path, "w", encoding="utf-8") as f:
            json.dump(
                self.test_orchestration.to_dict(), f, indent=2, ensure_ascii=False
            )

        # 加载状态应该从备份恢复
        loaded_orchestration = self.state_manager.load_state_with_fallback()

        # 验证从备份加载的对象
        self.assertIsNotNone(loaded_orchestration)
        self.assertEqual(loaded_orchestration.task_id, "test-task-123")

    def test_load_state_with_fallback_no_files(self):
        """测试带回退的加载（无任何文件）"""
        # 确保文件都不存在
        state_file_path = self.state_manager.get_state_file_path()
        backup_file_path = self.state_manager.get_backup_file_path()

        if state_file_path.exists():
            state_file_path.unlink()
        if backup_file_path.exists():
            backup_file_path.unlink()

        # 加载状态应该返回 None
        loaded_orchestration = self.state_manager.load_state_with_fallback()
        self.assertIsNone(loaded_orchestration)

    def test_state_file_exists(self):
        """测试检查状态文件是否存在"""
        # 文件不存在
        self.assertFalse(self.state_manager.state_file_exists())

        # 保存状态后文件应该存在
        self.state_manager.save_state(self.test_orchestration)
        self.assertTrue(self.state_manager.state_file_exists())

    def test_backup_file_exists(self):
        """测试检查备份文件是否存在"""
        # 备份文件不存在
        self.assertFalse(self.state_manager.backup_file_exists())

        # 保存两次后备份文件应该存在
        self.state_manager.save_state(self.test_orchestration)
        self.test_orchestration.location = "Hamburg, Germany"
        self.state_manager.save_state(self.test_orchestration)
        self.assertTrue(self.state_manager.backup_file_exists())

    def test_remove_state_file(self):
        """测试删除状态文件"""
        # 保存状态
        self.state_manager.save_state(self.test_orchestration)
        state_file_path = self.state_manager.get_state_file_path()
        self.assertTrue(state_file_path.exists())

        # 删除状态文件
        result = self.state_manager.remove_state_file()
        self.assertTrue(result)
        self.assertFalse(state_file_path.exists())

        # 删除不存在的文件也应该成功
        result = self.state_manager.remove_state_file()
        self.assertTrue(result)

    def test_get_state_info(self):
        """测试获取状态信息"""
        # 获取初始信息
        info = self.state_manager.get_state_info()
        self.assertEqual(info["work_dir"], str(self.temp_dir))
        self.assertEqual(info["state_file"], self.state_file)
        self.assertFalse(info["state_file_exists"])
        self.assertTrue(info["backup_enabled"])
        self.assertFalse(info["backup_file_exists"])

        # 保存状态后再次获取信息
        self.state_manager.save_state(self.test_orchestration)
        info = self.state_manager.get_state_info()
        self.assertTrue(info["state_file_exists"])
        self.assertIn("state_file_size", info)
        self.assertIn("state_file_mtime", info)

    def test_save_state_io_error(self):
        """测试保存状态时的IO错误"""
        # 创建一个不可写的目录
        readonly_dir = self.temp_dir / "readonly"
        readonly_dir.mkdir()
        readonly_dir.chmod(0o444)  # 只读权限

        # 创建指向只读目录的管理器
        readonly_manager = StateManager(work_dir=str(readonly_dir))

        # 保存应该失败并抛出 IOError
        with self.assertRaises(IOError):
            readonly_manager.save_state(self.test_orchestration)

    def test_load_state_io_error(self):
        """测试加载状态时的IO错误"""
        # 保存状态
        self.state_manager.save_state(self.test_orchestration)

        # 修改文件权限为不可读
        state_file_path = self.state_manager.get_state_file_path()
        state_file_path.chmod(0o222)  # 只写权限

        # 加载应该失败并抛出 IOError
        with self.assertRaises(IOError):
            self.state_manager.load_state()

    def test_load_state_invalid_json(self):
        """测试加载状态时的无效JSON"""
        # 创建包含无效JSON的文件
        state_file_path = self.state_manager.get_state_file_path()
        with open(state_file_path, "w") as f:
            f.write("invalid json content")

        # 加载应该失败并抛出 ValueError
        with self.assertRaises(ValueError):
            self.state_manager.load_state()

    def test_temp_file_cleanup_on_error(self):
        """测试错误时临时文件的清理"""
        # 模拟序列化失败
        with patch("json.dump", side_effect=TypeError("Serialization error")):
            with self.assertRaises(TypeError):
                self.state_manager.save_state(self.test_orchestration)

        # 临时文件应该被清理
        temp_file_path = self.state_manager.get_temp_file_path()
        self.assertFalse(temp_file_path.exists())


class TestFactoryFunctions(unittest.TestCase):
    """测试工厂函数"""

    def test_create_state_manager(self):
        """测试创建状态管理器"""
        manager = create_state_manager(
            work_dir="/tmp/test", state_file="custom_state.json", backup_enabled=False
        )

        self.assertEqual(manager.work_dir, Path("/tmp/test"))
        self.assertEqual(manager.state_file, "custom_state.json")
        self.assertFalse(manager.backup_enabled)


if __name__ == "__main__":
    unittest.main()
