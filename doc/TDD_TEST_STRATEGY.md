# 全面测试驱动开发 (TDD) 策略

**日期:** 2025-08-04

**作者:** Roo

**状态:** **已更新**

## 1. 核心理念

本策略旨在为 `google-maps-grid-search` 项目建立一套完整的、遵循测试驱动开发（TDD）理念的测试体系。我们的目标是：**让测试定义需求，让需求驱动开发**。所有新功能和重构都应遵循“红-绿-重构”的开发周期。

## 2. 测试金字塔

我们将构建一个三层测试金字塔，以平衡测试覆盖率、执行速度和维护成本。

- **L1 - 单元测试 (70%)**: 快速、隔离地测试单个函数和类的逻辑。
- **L2 - 集成测试 (20%)**: 验证模块间的协作是否正确。
- **L3 - 端到端测试 (10%)**: 模拟真实用户场景，验证完整业务流程。

## 3. 模块化测试规格

### 3.1. `src/grid_algorithms.py` (单元测试)

- **目标**: 确保所有地理和数学计算的精确性。
- **现有测试**: `test_grid_algorithms.py` 已覆盖大部分核心函数，基础良好。
- **TDD 新增/强化**:
    - **`generate_grid_points`**:
        - **TDD 待办**: 鉴于项目主要目标区域（如洛杉矶）位于中纬度，我们将暂缓对赤道和极地等极端地理位置的测试，以集中资源。
    - **`calculate_search_parameters`**:
        - **红**: 编写一个测试，输入一个极窄但极长的“走廊”形边界框，断言计算出的初始半径是合理的，而不是过大或过小。

### 3.2. `src/data_models.py` (单元测试)

- **目标**: 保证数据结构的稳定性和业务逻辑的正确性。
- **现有测试**: `test_data_models.py` 覆盖全面。
- **TDD 重构**:
    - **`TestFactoryFunctions`**: **移除**对旧工厂函数的测试。
    - **`SearchOrchestration`**:
        - **红**: 编写一个测试，在 `SearchOrchestration` 中添加10000个 `GridCell`，验证 `get_all_place_ids` 等聚合操作的性能在可接受范围内。
    - **`SearchStatus` 枚举**:
        - **绿**: **移除** `FAILED` 状态。由于我们采用了“遇错即停，保持PENDING”的策略，此状态已冗余。同时移除所有相关的测试用例。

### 3.3. `src/api_client.py` (集成测试)

- **目标**: 确保与外部API（无论是真实的还是模拟的）的交互是可靠和有弹性的。
- **现有测试**: `test_api_client.py` 提供了基础。
- **TDD 新增/强化**:
    - **`perform_nearby_search`**:
        - **红**: 编写一个测试，模拟API在返回第一页数据后，第二页返回 `OVER_QUERY_LIMIT` 错误。断言客户端能够正确处理这种情况，返回第一页的数据并记录警告，而不是崩溃。
    - **`get_bounding_box`**:
        - **红**: 编写一个测试，模拟地理编码API返回多个结果。断言客户端总是稳定地选择**第一个**结果的边界框。

### 3.4. `src/grid_engine.py` (集成测试)

- **目标**: 验证核心业务逻辑——分层处理和细化决策的正确性。
- **现有测试**: `test_grid_engine.py` 覆盖了大部分情况。
- **TDD 新增/强化**:
    - **`process_next_layer` (原子化处理与保存)**:
        - **红**: 编写一个测试，注入一个模拟的 `StateManager` 和 `APIClient`。让 `APIClient` 在处理第二个单元格时抛出 `APIError`。
        - **断言**:
            1. `state_manager.save_state` 在此过程中总共只被调用了**一次**（在第一个单元格成功处理后）。
            2. `process_next_layer` 方法最终向上抛出了 `APIError` 异常。
            3. 检查传递给 `save_state` 的 `orchestration` 对象：第一个单元格的状态应为 `SEARCH_COMPLETE`，而第二个（失败的）单元格的状态应**保持为 `PENDING`**。
    - **`_plan_next_layer`**:
        - **红**: 编写一个测试，让一个父单元的 `layer_id` 达到 `max_refinement_levels` 的上限。断言即使其结果数超过阈值，程序也**不会**再为它生成下一层子网格。

### 3.5. `src/main.py` (端到端测试)

- **目标**: 验证整个应用的业务流程在不同用户输入下的表现。
- **现有测试**: `test_integration.py` 提供了基础框架。
- **TDD 新增/强化**:
    - **命令行参数验证**:
        - **红**: 编写一个测试，通过命令行传入 `--initial-radius` 但**不传入** `--initial-grid-step`。断言程序应能优雅地处理这种情况（例如，报错退出或使用默认步长因子）。
    - **状态恢复与重构的兼容性**:
        - **红**: 编写一个测试，先用**旧的代码逻辑**（单一根单元）生成一个 `orchestration.json` 状态文件。然后，用**重构后的新代码**尝试加载并恢复这个旧状态。断言程序能够兼容旧状态并继续执行。

### 3.6. 健壮性与状态恢复 (全新端到端测试)

- **目标**: 验证“处理一格，保存一次，遇错即停”的策略在真实场景下的端到端表现。
- **测试场景**: 模拟一次因API调用失败而中断的搜索，然后验证程序能否从断点完美恢复。
- **步骤**:
    1.  **红 (第一次运行 - 模拟失败)**:
        - 编写一个端到端测试，使用一个会失败的模拟 `APIClient`（例如，在第二次调用时抛出 `APIRateLimitError`）。
        - 运行 `main()` 函数，并断言它会因为 `APIRateLimitError` 而退出。
        - **断言状态文件**: 读取生成的 `orchestration.json` 文件。验证第一个网格单元已被成功处理（状态为 `SEARCH_COMPLETE`），而第二个网格单元的状态**仍然是 `PENDING`**。
    2.  **绿 (第二次运行 - 验证恢复)**:
        - 在同一个测试中，将模拟 `APIClient` 替换为一个总是成功的客户端。
        - 再次运行 `main()` 函数（使用相同的 `--work-dir`）。
        - **断言**:
            - 程序应能成功运行至结束，不再抛出异常。
            - 最终的 `orchestration.json` 文件显示所有网格单元都已处理完毕。

## 4. 执行流程

1.  **评审**: 请您评审此TDD策略。
2.  **迁移**: 将此策略中的测试用例，以注释的形式（例如 `@pytest.mark.todo`）添加到相应的测试文件中。
3.  **开发**: 遵循“红-绿-重构”的循环，逐一实现这些测试用例，并编写或修改代码使其通过。
4.  **持续集成**: 将完整的测试套件集成到CI/CD流程中，确保未来的每一次提交都会自动运行所有测试。

这份策略将为我们的项目奠定坚实的质量基础。