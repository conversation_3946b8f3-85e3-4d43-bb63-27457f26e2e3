# Google Maps Grid Search - 模块化架构文档

## 📋 概述

这是一个重构后的模块化版本，从单体 `grid_search.py` 演变而来。采用分层架构和依赖注入模式，提供更好的可维护性、可测试性和可扩展性。

## 🏗️ 架构设计

### 核心架构原则

1. **分层架构** - 清晰的职责分离
2. **依赖注入** - 通过 `ContextFactory` 管理配置
3. **接口抽象** - API 层使用接口定义
4. **单一职责** - 每个模块专注单一功能

### 模块层次结构

```
┌─────────────────────────────────────┐
│           Application Layer          │
│             (main.py)               │
├─────────────────────────────────────┤
│          Domain Layer               │
│  (config, grid_algorithms, context)│
├─────────────────────────────────────┤
│        Service Layer                │
│ (api_client, search_processor,     │
│  grid_processor, progress_manager) │
├─────────────────────────────────────┤
│        Infrastructure Layer         │
│    (api/, visualization, report_)   │
└─────────────────────────────────────┘
```

## 📁 模块详解

### 1. 核心模块

#### 🚀 `main.py` - 应用程序入口
- **职责**: 协调所有模块，执行主流程
- **关键功能**:
  - 命令行参数解析
  - 上下文初始化
  - 主循环控制
  - 特殊模式处理（地图合并、参数测试）

#### ⚙️ `context.py` - 配置管理
- **职责**: 集中管理所有配置和状态
- **关键组件**:
  - `SearchConfig`: 搜索相关配置
  - `APIConfig`: API 配置
  - `FilePaths`: 文件路径管理
  - `SearchState`: 搜索状态跟踪
  - `APIState`: API 状态管理
  - `VisualizationState`: 可视化状态
  - `ContextFactory`: 配置工厂

#### 🛠️ `config.py` - 配置解析
- **职责**: 命令行参数解析和环境配置
- **功能**: 
  - 创建命令行解析器
  - 加载环境变量
  - 验证参数

### 2. 业务逻辑模块

#### 🌐 `grid_algorithms.py` - 网格算法
- **职责**: 地理空间计算和网格生成
- **核心算法**:
  - 哈夫赛因距离计算
  - 边界框计算
  - 自适应网格生成
  - 参数动态计算

#### 🔍 `grid_processor.py` - 网格处理
- **职责**: 网格点处理和细化逻辑
- **功能**:
  - 网格点处理流程
  - 自适应细化决策
  - 搜索结果分析

#### 📊 `search_processor.py` - 搜索处理
- **职责**: 搜索执行和结果处理
- **功能**:
  - 单点搜索执行
  - 分页处理
  - 结果分析和去重
  - 细化搜索执行

#### 📈 `progress_manager.py` - 进度管理
- **职责**: 持久化和恢复状态
- **功能**:
  - 进度保存/加载
  - Place ID 管理
  - 详细数据保存
  - CSV 报告生成

### 3. 基础设施模块

#### 🔌 `api_client.py` - API 客户端
- **职责**: API 交互的统一接口
- **功能**:
  - 附近搜索调用
  - 分页处理
  - 错误处理和重试
  - 结果处理

#### 🌐 `api/` - API 抽象层
- **职责**: 提供 API 的抽象实现
- **组件**:
  - `interface.py`: API 接口定义
  - `google_client.py`: Google API 实现
  - `mock_client.py`: 模拟 API 实现
  - `data_processor.py`: 数据处理

#### 🗺️ `visualization.py` - 可视化
- **职责**: 地图生成和可视化
- **功能**:
  - 交互式地图生成
  - 搜索结果可视化
  - 数据保存和加载
  - 多地图合并

#### 📝 `report_generator.py` - 报告生成
- **职责**: 文件管理和报告生成
- **功能**:
  - 文件命名管理
  - 目录结构创建
  - 最终报告生成
  - 地图数据合并

#### 🧪 `test_utils.py` - 测试工具
- **职责**: 参数敏感度测试
- **功能**:
  - 参数组合测试
  - 性能指标计算
  - 结果分析和报告

## 🔄 数据流

### 搜索流程

```mermaid
graph TD
    A[main.py] --> B[ContextFactory.create_from_args]
    B --> C[search_processor.perform_search_at_point]
    C --> D[api_client.perform_nearby_search]
    D --> E[api.google_client/mock_client]
    E --> F[api.data_processor]
    F --> G[progress_manager.save_place_ids]
    G --> H[grid_processor.analyze_results]
    H --> I{需要细化?}
    I -->|是| J[search_processor.perform_refined_search]
    I -->|否| K[标记完成]
    J --> C
    K --> L{所有点完成?}
    L -->|否| C
    L -->|是| M[visualization.visualize_search_results]
    M --> N[report_generator.generate_final_report]
```

### 配置流

```mermaid
graph TD
    A[命令行参数] --> B[config.create_parser]
    B --> C[ContextFactory]
    C --> D[SearchConfig]
    C --> E[APIConfig]
    C --> F[FilePaths]
    C --> G[SearchState]
    C --> H[APIState]
    C --> I[VisualizationState]
```

## 🚀 使用方法

### 基本使用

```bash
# 基本搜索
python -m src.main --location "Berlin, Germany" --place-type "restaurant"

# 试运行
python -m src.main --location "Berlin, Germany" --place-type "restaurant" --dry-run

# 参数测试
python -m src.main --param-test --test-area alexanderplatz

# 可视化
python -m src.main --location "Berlin, Germany" --place-type "restaurant" --visualize
```

### 环境配置

创建 `.env` 文件：
```
GOOGLE_MAPS_API_KEY=your_api_key_here
```

### 高级功能

#### 参数敏感性测试

```bash
# 在预定义测试区域运行参数测试
python -m src.main --param-test --test-area tiergarten
```

输出示例：
```
参数敏感性测试结果:
+------+---------+------+----------+------+
| 半径 | 网格步长 | 阈值 | API调用数 | 地点数 |
+------+---------+------+----------+------+
| 1000 |   500   |  40  |    20    | 156  |
| 1500 |   750   |  50  |    15    | 142  |
+------+---------+------+----------+------+

推荐配置: 半径=1000m, 网格步长=500m, 阈值=40
```

#### 地图合并

```bash
# 合并多个地图数据文件
python -m src.main --combine-maps map_data_1.json map_data_2.json map_data_3.json
```

## 📊 输出文件

### 文件结构

```
output/
├── [work_dir]/
│   ├── place_ids_[type]_[location]_[mode].txt
│   ├── progress_[type]_[location]_[mode].txt
│   ├── refinements_[type]_[location]_[mode].txt
│   ├── map_data_[type]_[location]_[mode].json
│   ├── map_[type]_[location]_[mode].html
│   ├── [type]_summary_[location]_[mode]_[timestamp].csv
│   └── detailed_place_data/
│       ├── mock_place_[density]_[id]_[index].json
│       └── ...
└── ...
```

### 文件说明

- **`place_ids_*.txt`**: 找到的唯一 Place ID 列表
- **`progress_*.txt`**: 搜索进度，支持断点续传
- **`refinements_*.txt`**: 细化搜索日志
- **`map_data_*.json`**: 地图数据，支持合并
- **`map_*.html`**: 交互式可视化地图
- **`*_summary_*.csv`**: CSV 格式的汇总报告
- **`detailed_place_data/`**: 详细地点信息（JSON）

## 🧪 测试

### 内置测试区域

脚本预定义了几个测试区域用于开发和测试：

- **`alexanderplatz`**: 高密度区域
- **`tiergarten`**: 低密度区域  
- **`kreuzberg`**: 混合密度区域
- **`friedrichstrasse`**: 商业区域

### 完全免费的试运行

```bash
# 完全无 API 调用的试运行
python -m src.main --test-area alexanderplatz --dry-run --visualize
```

## 🔧 配置参数

### 搜索参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `target_location` | "Berlin, Germany" | 搜索目标位置 |
| `place_type` | "physiotherapist" | 地点类型 |
| `initial_radius` | 动态计算 | 初始搜索半径 |
| `initial_grid_step` | 动态计算 | 网格步长 |
| `subdivision_threshold` | 40 | 细化阈值 |

### API 参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `api_key` | 环境变量 | Google Maps API 密钥 |
| `max_calls` | 0 | 最大 API 调用数 (0=无限制) |
| `rate_limit_delay` | 1.0 | 速率限制延迟 |
| `max_retries` | 3 | 最大重试次数 |

## 🎯 架构优势

### 1. 可维护性
- 清晰的模块职责分离
- 配置集中管理
- 统一的错误处理

### 2. 可测试性
- 接口抽象便于 Mock
- 独立的模块易于单元测试
- 内置参数测试功能

### 3. 可扩展性
- 插件式 API 架构
- 模块化设计支持新功能
- 配置驱动参数调整

### 4. 可复用性
- 核心算法独立封装
- API 层抽象支持多种实现
- 工具函数可单独使用

## 🔄 从单体版本的迁移

### 主要改进

1. **架构重构**: 从单一文件到模块化架构
2. **配置管理**: 从硬编码到动态配置
3. **API 抽象**: 从直接调用到接口抽象
4. **状态管理**: 从文件到对象状态
5. **错误处理**: 从基础到完善的错误处理

### 向后兼容性

- 保持了相同的命令行接口
- 输出文件格式保持一致
- 核心搜索算法不变
- 所有原有功能都被保留

## 📈 性能优化

### 搜索优化

- **自适应网格**: 根据结果密度自动调整
- **智能细化**: 仅在高密度区域细化
- **缓存机制**: 避免重复搜索
- **分页优化**: 高效处理 API 分页

### 资源管理

- **内存效率**: 流式处理大量数据
- **磁盘优化**: 增量式文件写入
- **并发控制**: 可配置的并发限制
- **进度保存**: 支持长时间运行的断点续传

## 🔒 安全性

### API 安全

- **密钥管理**: 通过环境变量安全存储
- **速率限制**: 遵守 API 使用限制
- **错误处理**: 安全的错误信息处理
- **重试机制**: 指数退避重试

### 数据安全

- **本地存储**: 所有数据本地处理
- **文件权限**: 适当的文件访问控制
- **输入验证**: 严格的参数验证
- **日志记录**: 详细的操作日志

---

*版本 2.0.0 - 模块化架构重构版本*