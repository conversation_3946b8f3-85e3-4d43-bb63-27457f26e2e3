# 性能基准测试方案

根据 REFACTORING_PLAN.md 任务-4.2 的要求，设计本性能基准测试方案，用于对比新旧架构在相同任务下的性能表现。

## 1. 测试指标

### 1.1 核心性能指标

1. **处理时间 (Processing Time)**
   - 总执行时间（从开始到完成）
   - 各阶段执行时间（初始化、网格处理、细化处理、数据保存等）

2. **内存占用 (Memory Usage)**
   - 峰值内存使用量
   - 平均内存使用量
   - 内存增长趋势

3. **API调用次数 (API Call Count)**
   - 总API调用次数
   - 成功调用次数
   - 失败重试次数

### 1.2 次要性能指标

1. **地点发现数量 (Places Found)**
   - 发现的唯一地点ID数量
   - 重复地点ID数量

2. **网格处理效率 (Grid Processing Efficiency)**
   - 每秒处理的网格单元数
   - 细化触发率

3. **系统资源使用 (System Resource Usage)**
   - CPU使用率
   - 磁盘I/O操作次数

## 2. 测试用例设计

### 2.1 测试区域选择

选择以下具有代表性的测试区域，以覆盖不同的场景：

1. **密集区域 (Dense Area)**
   - 测试区域: alexanderplatz
   - 特点: 地点密度高，容易触发细化
   - 预期: 大量API调用，多层次细化

2. **稀疏区域 (Sparse Area)**
   - 测试区域: tiergarten
   - 特点: 地点密度低，很少触发细化
   - 预期: 较少API调用，主要在根层处理

3. **混合区域 (Mixed Area)**
   - 测试区域: kreuzberg
   - 特点: 地点密度中等，部分区域密集部分稀疏
   - 预期: 中等API调用，局部细化

### 2.2 参数配置

使用以下标准参数进行测试：

- `--place-type`: restaurant (统一地点类型)
- `--dry-run`: true (使用模拟数据避免真实API调用)
- `--mock-seed`: 42 (固定随机种子确保结果可重现)
- `--max-calls`: 0 (无限制，确保完整执行)

## 3. 测试方法

### 3.1 新架构测试方法

#### 3.1.1 性能监控工具

使用以下工具监控性能：

1. **时间测量**
   ```python
   import time
   start_time = time.time()
   # 执行测试
   end_time = time.time()
   elapsed_time = end_time - start_time
   ```

2. **内存监控**
   ```python
   import psutil
   import os
   
   process = psutil.Process(os.getpid())
   memory_info = process.memory_info()
   peak_memory = memory_info.rss / 1024 / 1024  # MB
   ```

3. **API调用统计**
   - 通过API客户端内部计数器统计

#### 3.1.2 测试执行流程

1. **环境准备**
   - 确保测试环境一致（Python版本、依赖包版本）
   - 清理历史测试数据

2. **执行测试**
   ```bash
   python -m src.main --test-area alexanderplatz --dry-run --mock-seed 42
   ```

3. **数据收集**
   - 记录执行时间、内存使用、API调用次数等指标
   - 保存测试结果到JSON文件

4. **重复测试**
   - 每个测试用例执行3次，取平均值
   - 确保结果的稳定性和可重现性

### 3.2 旧架构测试方法

#### 3.2.1 性能监控工具

使用相同的方法监控旧架构性能：

1. **时间测量**
   ```python
   import time
   start_time = time.time()
   # 执行测试
   end_time = time.time()
   elapsed_time = end_time - start_time
   ```

2. **内存监控**
   ```python
   import psutil
   import os
   
   process = psutil.Process(os.getpid())
   memory_info = process.memory_info()
   peak_memory = memory_info.rss / 1024 / 1024  # MB
   ```

3. **API调用统计**
   - 通过全局变量统计API调用次数

#### 3.2.2 测试执行流程

1. **环境准备**
   - 使用相同的测试环境
   - 清理历史测试数据

2. **执行测试**
   ```bash
   python deprecation_src/grid_search.py --test-area alexanderplatz --dry-run --mock-seed 42
   ```

3. **数据收集**
   - 记录执行时间、内存使用、API调用次数等指标
   - 保存测试结果到JSON文件

4. **重复测试**
   - 每个测试用例执行3次，取平均值

## 4. 对比方案

### 4.1 对比维度

1. **绝对性能对比**
   - 直接比较新旧架构在相同测试用例下的性能指标

2. **相对性能提升**
   - 计算新架构相对于旧架构的性能提升百分比
   - 识别性能改进和退步的方面

3. **资源使用效率**
   - 比较单位API调用的处理时间
   - 比较单位内存使用的处理能力

### 4.2 对比方法

#### 4.2.1 数据收集

1. **并行测试执行**
   - 在相同硬件环境下依次执行新旧架构测试
   - 确保系统状态一致（重启服务、清理缓存等）

2. **结果记录**
   - 使用统一的格式记录测试结果
   - 保存详细日志便于问题分析

#### 4.2.2 数据分析

1. **统计分析**
   - 计算平均值、标准差等统计指标
   - 识别异常值和数据波动

2. **可视化展示**
   - 使用图表展示性能对比结果
   - 突出关键性能差异

3. **报告生成**
   - 生成详细的性能对比报告
   - 提供改进建议和优化方向

### 4.3 验证标准

1. **性能提升目标**
   - 处理时间减少至少20%
   - 内存使用优化至少15%
   - API调用效率提升至少10%

2. **稳定性要求**
   - 新架构在所有测试用例中应稳定运行
   - 无内存泄漏或性能退化问题

3. **功能一致性**
   - 新旧架构应产生相同或更优的搜索结果
   - 确保功能正确性不受影响

## 5. 测试执行计划

### 5.1 测试环境

- **硬件配置**: 统一的测试机器（CPU、内存、磁盘）
- **软件环境**: 相同的Python版本和依赖包版本
- **网络环境**: 稳定的网络连接（对于模拟测试影响较小）

### 5.2 测试顺序

1. **旧架构测试**
   - 先执行旧架构测试，建立基准线
   - 记录基准性能数据

2. **新架构测试**
   - 执行新架构测试
   - 与基准数据进行对比

### 5.3 测试报告

测试完成后生成包含以下内容的报告：

1. **测试概述**
   - 测试目标和范围
   - 测试环境描述

2. **性能对比结果**
   - 详细的数据表格
   - 直观的图表展示

3. **分析和结论**
   - 性能改进分析
   - 存在的问题和改进建议

4. **附录**
   - 详细的测试日志
   - 测试脚本和配置

## 6. 风险和缓解措施

### 6.1 潜在风险

1. **环境不一致**
   - 硬件或软件环境差异影响测试结果准确性

2. **数据波动**
   - 系统负载或其他进程影响测试结果稳定性

3. **测试覆盖不全**
   - 测试用例不能充分代表实际使用场景

### 6.2 缓解措施

1. **环境控制**
   - 使用专用测试机器
   - 测试前重启系统，关闭无关进程

2. **多次测试**
   - 每个测试用例执行多次，取平均值
   - 排除异常数据点

3. **扩展测试用例**
   - 增加更多具有代表性的测试区域
   - 考虑不同地点类型和参数配置

## 7. 附录

### 7.1 测试脚本示例

```python
# performance_test.py
import time
import psutil
import os
import json
import subprocess

def run_performance_test(architecture, test_area):
    """运行性能测试"""
    process = psutil.Process(os.getpid())
    
    # 记录初始状态
    start_time = time.time()
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    # 执行测试
    if architecture == "new":
        cmd = ["python", "-m", "src.main", "--test-area", test_area, "--dry-run", "--mock-seed", "42"]
    else:
        cmd = ["python", "deprecation_src/grid_search.py", "--test-area", test_area, "--dry-run", "--mock-seed", "42"]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    # 记录结束状态
    end_time = time.time()
    final_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    # 计算指标
    elapsed_time = end_time - start_time
    peak_memory = max(initial_memory, final_memory)
    
    return {
        "architecture": architecture,
        "test_area": test_area,
        "elapsed_time": elapsed_time,
        "peak_memory": peak_memory,
        "exit_code": result.returncode
    }

# 执行测试
test_areas = ["alexanderplatz", "tiergarten", "kreuzberg"]
results = []

for area in test_areas:
    # 测试旧架构
    old_result = run_performance_test("old", area)
    results.append(old_result)
    
    # 测试新架构
    new_result = run_performance_test("new", area)
    results.append(new_result)

# 保存结果
with open("performance_results.json", "w") as f:
    json.dump(results, f, indent=2)
```

### 7.2 测试结果分析示例

```python
# analyze_results.py
import json

def analyze_performance_results():
    """分析性能测试结果"""
    with open("performance_results.json", "r") as f:
        results = json.load(f)
    
    # 按测试区域分组
    by_area = {}
    for result in results:
        area = result["test_area"]
        if area not in by_area:
            by_area[area] = {"old": None, "new": None}
        by_area[area][result["architecture"]] = result
    
    # 生成对比报告
    print("性能对比报告")
    print("=" * 50)
    
    for area, data in by_area.items():
        old_data = data["old"]
        new_data = data["new"]
        
        if old_data and new_data:
            time_improvement = (old_data["elapsed_time"] - new_data["elapsed_time"]) / old_data["elapsed_time"] * 100
            memory_improvement = (old_data["peak_memory"] - new_data["peak_memory"]) / old_data["peak_memory"] * 100
            
            print(f"\n测试区域: {area}")
            print(f"  处理时间: {old_data['elapsed_time']:.2f}s -> {new_data['elapsed_time']:.2f}s "
                  f"({time_improvement:+.1f}%)")
            print(f"  内存使用: {old_data['peak_memory']:.1f}MB -> {new_data['peak_memory']:.1f}MB "
                  f"({memory_improvement:+.1f}%)")

if __name__ == "__main__":
    analyze_performance_results()
```