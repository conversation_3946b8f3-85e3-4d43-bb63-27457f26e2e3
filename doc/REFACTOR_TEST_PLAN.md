# 重构后测试方案

**日期:** 2025-08-04

**作者:** Roo

**状态:** 待评审

## 1. 测试目标

本测试方案旨在全面验证“静态预生成网格”重构的正确性、可靠性和鲁棒性。核心目标是确保新的网格生成逻辑符合预期，同时保证现有功能不发生退化。

- **验证参数化**：当用户通过命令行提供 `--initial-radius` 和 `--initial-grid-step` 时，程序必须严格使用这些值生成初始网格。
- **验证自动计算**：当用户不提供参数时，程序必须能自动计算出合理的初始网格。
- **验证数据一致性**：所有初始生成的网格单元，其 `layer_id` 必须为 `0`。
- **验证无缝集成**：`GridEngine` 必须能正确处理预生成的、包含多个点的第0层网格。
- **确保无功能退化**：确保后续的细化、状态管理和报告生成等功能不受影响。

## 2. 测试范围

### 2.1. 单元测试 (Unit Tests)

| 模块 | 文件 | 新增/修改测试用例 | 测试目的 |
| :--- | :--- | :--- | :--- |
| **配置** | `tests/test_config.py` | **新增** `test_new_grid_parameters` | 验证 `--initial-radius` 和 `--initial-grid-step` 参数能被正确解析和加载。 |
| **数据模型** | `tests/test_data_models.py` | **修改** `TestFactoryFunctions` | 移除对已删除的 `create_root_cell` 和 `create_search_orchestration` 的测试，确保测试文件能通过。 |
| **主逻辑** | `tests/test_main.py` | **新增** `test_create_orchestration_with_user_params` | 模拟用户传入初始参数，断言生成的 `SearchOrchestration` 对象包含一个完整的、`layer_id=0` 的网格，且半径和步长与输入一致。 |
| | | **新增** `test_create_orchestration_with_auto_params` | 模拟用户不传入初始参数，断言程序调用了 `calculate_search_parameters`，并生成了一个完整的、`layer_id=0` 的网格。 |

### 2.2. 集成测试 (Integration Tests)

| 模块 | 文件 | 新增/修改测试用例 | 测试目的 |
| :--- | :--- | :--- | :--- |
| **端到端流程** | `tests/test_integration.py` | **修改** `test_full_workflow_simulation` | 更新此测试，使其不再依赖旧的工厂函数。改为通过模拟 `main` 函数的执行来创建一个包含预生成网格的状态文件，并验证其正确性。 |
| | | **新增** `test_e2e_with_user_params` | 运行一个完整的端到端流程（使用 `--dry-run`），传入 `--initial-radius` 和 `--initial-grid-step`。断言最终生成的 `orchestration.json` 文件中，所有 `layer_id=0` 的单元格数量和 `search_radius` 符合预期。 |
| | | **新增** `test_e2e_with_auto_params` | 运行一个完整的端到端流程（使用 `--dry-run`），不传入初始参数。断言最终生成的 `orchestration.json` 文件中，存在多个 `layer_id=0` 的单元格。 |

### 2.3. 手动/回归测试 (Manual/Regression Tests)

除了自动化测试，我们还将执行以下手动测试命令，以直观地验证最终结果。

1.  **测试用户指定参数（小范围）**
    ```bash
    python -m src.main --test-area alexanderplatz --dry-run --work-dir test_manual_user_params --visualize --initial-radius 500 --initial-grid-step 400 --max-calls 10
    ```
    - **预期结果**: 程序应立即生成一个覆盖 `alexanderplatz` 区域的、`layer_id=0` 的网格。所有网格点的搜索半径应为 `500m`。可视化地图 `visualization_map.html` 应清晰地展示这个初始网格。

2.  **测试自动计算参数（大范围）**
    ```bash
    python -m src.main --test-area losangeles --dry-run --work-dir test_manual_auto_params --visualize --max-calls 10
    ```
    - **预期结果**: 程序应自动计算出一个较大的初始半径和步长，并生成一个覆盖 `losangeles` 区域的、相对稀疏的、`layer_id=0` 的网格。可视化地图应展示这个稀疏的初始网格。

3.  **测试细化流程（回归）**
    ```bash
    python -m src.main --test-area alexanderplatz --dry-run --work-dir test_manual_refinement --visualize --initial-radius 200 --initial-grid-step 150 --max-calls 100
    ```
    - **预期结果**: 程序应首先生成一个较密的L0网格。由于 `alexanderplatz` 是密集区域，部分L0网格点的搜索结果应超过细化阈值，从而触发生成 `layer_id=1` 的子网格。可视化地图应同时展示蓝色的L0点和红色的L1细化点。

## 3. 测试环境

- **Python版本**: 3.x
- **依赖**: `pytest`, `unittest.mock`
- **配置**: 使用 `tests/algorithm_config.json` 作为算法配置文件。
- **模式**: 所有自动化和手动测试均在 `--dry-run` 模式下进行，以避免消耗API配额并确保测试结果的可重复性。

通过执行以上测试方案，我们可以高度自信地确保本次重构的质量。