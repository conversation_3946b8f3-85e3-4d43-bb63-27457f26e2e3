# Google Maps Grid Search 项目概览

**本文档是理解本项目当前架构、功能和用法的权威指南。**

---

### **1. 项目目标与核心功能**

本项目是一个 **Google Maps 网格搜索工具**，旨在系统性地、全面地从 Google Maps API 中提取特定类型的地点（Place）数据，以克服 `nearbySearch` API 单次最多返回 60 个结果的限制。

**核心功能和业务逻辑如下：**

*   **区域定义与网格生成**: 用户可以指定一个目标地点（`--location`）或使用预定义测试区域（`--test-area`）。程序通过 Geocoding API 获取地理边界框，并在此基础上**无条件地**生成一个覆盖整个区域的初始点网格（L0 网格）。
*   **分层、迭代的网格搜索**: 采用分层、广度优先（BFS）的搜索策略。从 L0 网格开始搜索，当发现某个网格单元的地点密度超过预设阈值时，会自动在该区域内部生成更密的子网格（L1, L2...）进行迭代细化搜索。
*   **状态管理与断点续传**: 整个搜索过程的状态（`SearchOrchestration` 对象）在关键步骤后都会被**原子化**持久化到 JSON 文件 (`orchestration.json`)。这使得程序具有强大的断点续传能力，在意外中断后可以从上次的状态精确恢复。
*   **数据处理与报告**: 程序会收集所有不重复的地点 ID，并在搜索完成后生成详细的统计报告和直观的可视化 HTML 地图。
*   **健壮性与可配置性**: 包含健壮的 API 客户端（处理错误和速率限制）、模拟模式（`--dry-run`）以及通过 `algorithm_config.json` 和命令行参数实现的高度可配置性。

---

### **2. 核心架构解析 (v2.0)**

项目采用**数据驱动（Data-Driven）**和**状态机（State Machine）**相结合的架构，是一个设计精良的**单体应用（Monolithic Application）**。该架构已经从早期脆弱的递归设计成功重构而来。

#### **2.1 核心设计原则**

*   **数据驱动**: 核心逻辑围绕一个显式的、结构化的状态对象 `SearchOrchestration` 展开。所有业务逻辑都作用于这个对象，而不是隐式地存储在调用栈或分散的文件中。
*   **广度优先搜索 (BFS)**: 搜索按层级进行，先处理完当前层的所有网格单元，再进入下一层。这消除了递归带来的栈溢出风险，并为未来的并行化处理奠定了基础。
*   **原子化状态管理**: 状态的保存和加载是原子操作，确保了在任何时刻中断任务，都不会导致数据损坏或状态丢失。

#### **2.2 关键模块与职责**

*   **`src/main.py`**: 流程编排器。负责初始化各个组件，并驱动整个搜索任务的执行流程。
*   **`src/data_models.py`**: 定义核心数据结构，是应用的“单一事实来源”。
    *   `SearchOrchestration`: 顶层状态对象，包含所有网格单元 (`cells`)、层级信息 (`layers`) 和任务配置。
    *   `GridCell`: 代表单个搜索单元，封装其坐标、状态、层级、父子关系和搜索结果。
    *   `SearchStatus`: 定义网格单元可能的状态（如 `PENDING`, `PROCESSING`, `REFINEMENT_NEEDED` 等）。
*   **`src/state_manager.py`**: 负责 `SearchOrchestration` 对象的原子化持久化和加载。通过“写入临时文件后重命名”的策略确保了操作的原子性。
*   **`src/grid_engine.py`**: 核心业务逻辑引擎。接收 `SearchOrchestration` 对象，执行分层搜索、API调用、细化决策，并更新状态对象。
*   **`src/config.py`**: 统一的配置管理入口。
    *   `create_parser()`: 定义和解析所有命令行参数。
    *   `load_config()`: 加载环境变量、`algorithm_config.json` 和命令行参数，合并成最终的配置字典。
*   **`src/api_client.py`**: 统一的 API 交互层。封装了对 Google Maps API (Geocoding, Nearby Search) 的调用，并处理错误、重试和速率限制。
*   **`src/grid_algorithms.py`**: 封装纯粹的地理计算算法，如生成网格点、计算搜索参数、哈夫斯距离等。
*   **`src/report_generator.py` & `src/visualization.py`**: 结果展示模块。根据 `SearchOrchestration` 对象生成最终的文本报告和 HTML 可视化地图。

---

### **3. 快速上手指南**

#### **3.1 环境准备**

1.  **安装依赖**:
    ```bash
    uv sync
    ```
2.  **设置 API Key**:
    在项目根目录创建一个 `.env` 文件，并添加你的 Google Maps API Key：
    ```
    GOOGLE_MAPS_API_KEY=YOUR_API_KEY_HERE
    ```

#### **3.2 基本用法**

*   **在指定位置搜索餐厅**:
    ```bash
    python src/main.py --location "San Francisco, CA" --place-type restaurant --visualize
    ```
    这将在 `./output/` 目录下生成报告和可视化地图。

*   **使用预定义测试区域**:
    ```bash
    python src/main.py --test-area alexanderplatz --place-type cafe --max-calls 100
    ```
    这将在柏林的亚历山大广场区域搜索咖啡馆，并限制API调用次数为100次。

#### **3.3 关键命令行参数**

*   `--location <str>`: 要搜索的地点名称或地址。
*   `--place-type <str>`: 要搜索的地点类型 (e.g., `restaurant`, `cafe`, `gym`)。
*   `--test-area <str>`: 使用预定义的测试区域 (e.g., `alexanderplatz`, `losangeles`)。
*   `--initial-radius <float>`: **(新功能)** 初始网格的搜索半径（米）。如果未提供，则根据区域大小自动计算。
*   `--initial-grid-step <float>`: **(新功能)** 初始网格点之间的间距（米）。如果未提供，则根据区域大小自动计算。
*   `--max-calls <int>`: 最大API调用次数限制 (0 = 无限制)。
*   `--dry-run`: 模拟运行，使用模拟数据，不产生真实API调用。
*   `--visualize`: 在搜索完成后生成可视化HTML地图。
*   `--work-dir <str>`: 指定输出文件的工作目录 (默认为当前目录 `.`)。

---

### **4. 关键特性**

#### **4.1 可靠性与断点续传**

*   **机制**: 每当一个层级的网格处理完成，或者有重要的状态变更时，`StateManager` 都会将当前的 `SearchOrchestration` 对象完整地序列化并保存到 `orchestration.json` 文件中。
*   **恢复**: 当程序再次启动时，它会首先尝试加载 `orchestration.json`。如果文件存在且有效，程序将从上次中断的地方继续执行，而不是从头开始。
*   **原子性**: 状态保存操作是原子的，避免了程序在写入过程中崩溃导致文件损坏的风险。

#### **4.2 可配置性**

*   **命令行参数**: 提供了对搜索任务（如地点、类型、API限制）和初始网格生成（`--initial-radius`, `--initial-grid-step`）的即时控制。
*   **`algorithm_config.json`**: 这是一个核心的算法配置文件，允许你微调搜索行为，而无需修改代码。关键配置项包括：
    *   `search.max_radius`: API搜索的最大半径。
    *   `search.subdivision_threshold`: 触发网格细化的结果数量阈值。
    *   `search.max_refinement_levels`: 最大细化层级数。
    *   `search.min_refinement_radius`: 最小细化半径。
    *   `search.initial_radius_factor`: 自动计算初始半径时的因子。
    *   `search.grid_step_factor`: 自动计算网格步长时的因子。

#### **4.3 可视化与报告**

*   **最终报告 (`report.txt`)**: 包含任务摘要、API调用统计、找到的地点总数、处理的网格点数等关键信息。
*   **可视化地图 (`visualization_map.html`)**: 一个交互式HTML地图，直观地展示了：
    *   所有被搜索过的网格点。
    *   触发了细化的网格点。
    *   最终找到的所有地点的标记。
    *   这对于理解搜索覆盖范围和结果密度非常有帮助。

---

### **5. 文档与历史**

*   **本文档 (`doc/PROJECT_OVERVIEW.md`)**: **当前权威指南**，请优先阅读本文档以了解项目。
*   **`doc/archive/`**: 此目录下存放了项目历史演进过程中的重要文档，它们记录了从v1到v2的重构历程，具有参考价值，但其内容可能不完全反映当前实现。
    *   `TECHNICAL_DESIGN.md`: v2.0架构的原始技术设计文档。
    *   `REFACTORING_PLAN.md`: v2.0重构的详细任务清单。
*   **已移除的文档**: `GRID_GENERATION_REFACTOR_PLAN.md` 中描述的关于初始网格生成的重构**已经完成**，该文档因其过时的状态标记具有误导性，已被移除。

---

### **6. 结论**

Google Maps Grid Search 项目已经发展成为一个**健壮、高效且易于维护**的工具。其数据驱动的BFS架构、原子化的状态管理和清晰的模块化设计，确保了它在处理大规模、长时间搜索任务时的稳定性和可靠性。通过本文档，用户和开发者可以快速上手并深入理解系统的工作原理。